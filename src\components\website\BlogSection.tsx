import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Calendar, User, ArrowRight, Clock } from "lucide-react";

const blogPosts = [
  {
    id: 1,
    title: "10 Tips for Perfect Wedding Photography",
    excerpt: "Discover the secrets to capturing stunning wedding moments that couples will treasure forever.",
    author: "Om Digital Studio",
    date: "Dec 15, 2023",
    readTime: "5 min read",
    category: "Wedding Tips",
    image: "/src/assets/studio-hero.jpg"
  },
  {
    id: 2,
    title: "Corporate Event Photography Best Practices",
    excerpt: "How to capture professional corporate events while maintaining the energy and atmosphere.",
    author: "Om Digital Studio",
    date: "Dec 10, 2023",
    readTime: "7 min read",
    category: "Corporate",
    image: "/src/assets/event-collage.jpg"
  },
  {
    id: 3,
    title: "Behind the Scenes: Our Latest Wedding Shoot",
    excerpt: "Take a peek behind the curtain of our recent wedding photography session in Bardoli.",
    author: "Om Digital Studio",
    date: "Dec 5, 2023",
    readTime: "4 min read",
    category: "Behind the Scenes",
    image: "/src/assets/studio-hero.jpg"
  },
  {
    id: 4,
    title: "Choosing the Right Photography Package",
    excerpt: "A comprehensive guide to selecting the perfect photography package for your special event.",
    author: "Om Digital Studio",
    date: "Nov 30, 2023",
    readTime: "6 min read",
    category: "Packages",
    image: "/src/assets/event-collage.jpg"
  },
  {
    id: 5,
    title: "Portrait Photography Lighting Techniques",
    excerpt: "Master the art of portrait lighting with these professional techniques and tips.",
    author: "Om Digital Studio",
    date: "Nov 25, 2023",
    readTime: "8 min read",
    category: "Techniques",
    image: "/src/assets/studio-hero.jpg"
  },
  {
    id: 6,
    title: "The Evolution of Digital Photography in Gujarat",
    excerpt: "How digital photography has transformed the wedding and event industry in Gujarat.",
    author: "Om Digital Studio",
    date: "Nov 20, 2023",
    readTime: "5 min read",
    category: "Industry",
    image: "/src/assets/event-collage.jpg"
  }
];

export default function BlogSection() {
  return (
    <section id="blog" className="py-20 bg-muted/30">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <Badge className="mb-4">Our Blog</Badge>
          <h2 className="text-4xl font-bold text-foreground mb-4">Photography Tips & Stories</h2>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
            Learn about photography techniques, behind-the-scenes stories, and industry insights
          </p>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {blogPosts.map((post) => (
            <Card key={post.id} className="group hover:shadow-primary transition-all duration-300 overflow-hidden">
              <div className="relative overflow-hidden">
                <img 
                  src={post.image} 
                  alt={post.title}
                  className="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
                />
                <div className="absolute top-3 left-3">
                  <Badge className="bg-background/90 text-foreground">{post.category}</Badge>
                </div>
              </div>
              
              <CardContent className="p-6">
                <h3 className="text-lg font-semibold mb-3 group-hover:text-primary transition-colors line-clamp-2">
                  {post.title}
                </h3>
                <p className="text-muted-foreground text-sm mb-4 line-clamp-3">
                  {post.excerpt}
                </p>
                
                <div className="flex items-center gap-4 text-xs text-muted-foreground mb-4">
                  <div className="flex items-center gap-1">
                    <User className="h-3 w-3" />
                    <span>{post.author}</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <Calendar className="h-3 w-3" />
                    <span>{post.date}</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <Clock className="h-3 w-3" />
                    <span>{post.readTime}</span>
                  </div>
                </div>

                <Button variant="outline" className="w-full gap-2 group-hover:bg-primary group-hover:text-primary-foreground">
                  Read More <ArrowRight className="h-4 w-4" />
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>

        <div className="text-center mt-12">
          <Button variant="outline" size="lg">
            View All Posts
          </Button>
        </div>
      </div>
    </section>
  );
}