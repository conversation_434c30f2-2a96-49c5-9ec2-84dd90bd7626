import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { 
  ShoppingCart, 
  Search, 
  Filter, 
  Plus, 
  Eye, 
  Download,
  CreditCard,
  Package,
  Truck,
  CheckCircle,
  Clock,
  AlertCircle
} from "lucide-react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { DashboardLayout } from "@/components/layout/DashboardLayout";

const orders = [
  {
    id: "ORD-2024-001",
    clientName: "Priya & Raj Wedding",
    eventDate: "2024-01-15",
    orderDate: "2024-01-20",
    items: ["Wedding Album (Premium)", "Digital Gallery Access", "USB Drive"],
    amount: "₹25,000",
    status: "completed",
    paymentStatus: "paid",
    deliveryStatus: "delivered"
  },
  {
    id: "ORD-2024-002", 
    clientName: "TechCorp Annual Event",
    eventDate: "2024-01-18",
    orderDate: "2024-01-22",
    items: ["Digital Downloads (500 photos)", "Event Highlights Video"],
    amount: "₹15,000",
    status: "processing",
    paymentStatus: "partial",
    deliveryStatus: "pending"
  },
  {
    id: "ORD-2024-003",
    clientName: "Baby Arjun Photoshoot", 
    eventDate: "2024-01-20",
    orderDate: "2024-01-21",
    items: ["Photo Prints (20 pics)", "Digital Album"],
    amount: "₹8,500",
    status: "confirmed",
    paymentStatus: "paid",
    deliveryStatus: "preparing"
  },
  {
    id: "ORD-2024-004",
    clientName: "Fashion Portfolio - Asha",
    eventDate: "2024-01-12",
    orderDate: "2024-01-25",
    items: ["Retouched Photos (50)", "Portfolio Album"],
    amount: "₹12,000",
    status: "draft",
    paymentStatus: "pending",
    deliveryStatus: "not_started"
  },
  {
    id: "ORD-2024-005",
    clientName: "Product Shoot - EcoFriendly",
    eventDate: "2024-01-10",
    orderDate: "2024-01-28",
    items: ["Product Photos (100)", "Background Removal", "Watermarks"],
    amount: "₹18,000",
    status: "completed", 
    paymentStatus: "paid",
    deliveryStatus: "delivered"
  }
];

const getStatusColor = (status: string) => {
  switch (status) {
    case "completed": return "bg-success/10 text-success border-success/20";
    case "processing": return "bg-warning/10 text-warning border-warning/20";
    case "confirmed": return "bg-accent/10 text-accent border-accent/20";
    case "draft": return "bg-muted/10 text-muted-foreground border-muted/20";
    default: return "bg-muted/10 text-muted-foreground border-muted/20";
  }
};

const getPaymentStatusColor = (status: string) => {
  switch (status) {
    case "paid": return "bg-success/10 text-success border-success/20";
    case "partial": return "bg-warning/10 text-warning border-warning/20";
    case "pending": return "bg-destructive/10 text-destructive border-destructive/20";
    default: return "bg-muted/10 text-muted-foreground border-muted/20";
  }
};

const getDeliveryStatusColor = (status: string) => {
  switch (status) {
    case "delivered": return "bg-success/10 text-success border-success/20";
    case "preparing": return "bg-accent/10 text-accent border-accent/20";
    case "pending": return "bg-warning/10 text-warning border-warning/20";
    case "not_started": return "bg-muted/10 text-muted-foreground border-muted/20";
    default: return "bg-muted/10 text-muted-foreground border-muted/20";
  }
};

export default function Orders() {
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");

  const filteredOrders = orders.filter(order => {
    const matchesSearch = order.clientName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         order.id.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === "all" || order.status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  return (
    <DashboardLayout>
      <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col lg:flex-row gap-4 justify-between items-start lg:items-center">
        <div>
          <h1 className="text-3xl font-bold text-foreground mb-2">
            Orders Management
          </h1>
          <p className="text-muted-foreground">
            Track and manage client orders, payments, and deliveries
          </p>
        </div>
        <Button className="gap-2">
          <Plus className="h-4 w-4" />
          Create Order
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card className="shadow-card border-border">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Orders</p>
                <p className="text-2xl font-bold">124</p>
              </div>
              <ShoppingCart className="h-8 w-8 text-primary" />
            </div>
            <p className="text-xs text-muted-foreground mt-2">+12% from last month</p>
          </CardContent>
        </Card>

        <Card className="shadow-card border-border">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Revenue</p>
                <p className="text-2xl font-bold">₹3,45,000</p>
              </div>
              <CreditCard className="h-8 w-8 text-success" />
            </div>
            <p className="text-xs text-muted-foreground mt-2">+18% from last month</p>
          </CardContent>
        </Card>

        <Card className="shadow-card border-border">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Pending</p>
                <p className="text-2xl font-bold">8</p>
              </div>
              <Clock className="h-8 w-8 text-warning" />
            </div>
            <p className="text-xs text-muted-foreground mt-2">2 urgent deliveries</p>
          </CardContent>
        </Card>

        <Card className="shadow-card border-border">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Completed</p>
                <p className="text-2xl font-bold">89</p>
              </div>
              <CheckCircle className="h-8 w-8 text-accent" />
            </div>
            <p className="text-xs text-muted-foreground mt-2">98% satisfaction rate</p>
          </CardContent>
        </Card>
      </div>

      {/* Filters and Search */}
      <Card className="shadow-card border-border">
        <CardContent className="p-6">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search orders by client name or order ID..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-full md:w-40">
                <SelectValue placeholder="Filter status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="draft">Draft</SelectItem>
                <SelectItem value="confirmed">Confirmed</SelectItem>
                <SelectItem value="processing">Processing</SelectItem>
                <SelectItem value="completed">Completed</SelectItem>
              </SelectContent>
            </Select>
            <Button variant="outline" className="gap-2">
              <Filter className="h-4 w-4" />
              More Filters
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Orders Table */}
      <Card className="shadow-card border-border">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Package className="h-5 w-5" />
            Recent Orders
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Order ID</TableHead>
                  <TableHead>Client</TableHead>
                  <TableHead>Items</TableHead>
                  <TableHead>Amount</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Payment</TableHead>
                  <TableHead>Delivery</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredOrders.map((order) => (
                  <TableRow key={order.id}>
                    <TableCell className="font-medium">{order.id}</TableCell>
                    <TableCell>
                      <div>
                        <p className="font-medium">{order.clientName}</p>
                        <p className="text-sm text-muted-foreground">Event: {order.eventDate}</p>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="space-y-1">
                        {order.items.slice(0, 2).map((item, index) => (
                          <p key={index} className="text-sm">{item}</p>
                        ))}
                        {order.items.length > 2 && (
                          <p className="text-xs text-muted-foreground">
                            +{order.items.length - 2} more items
                          </p>
                        )}
                      </div>
                    </TableCell>
                    <TableCell className="font-medium">{order.amount}</TableCell>
                    <TableCell>
                      <Badge variant="outline" className={getStatusColor(order.status)}>
                        {order.status}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <Badge variant="outline" className={getPaymentStatusColor(order.paymentStatus)}>
                        {order.paymentStatus}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <Badge variant="outline" className={getDeliveryStatusColor(order.deliveryStatus)}>
                        {order.deliveryStatus.replace('_', ' ')}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <div className="flex gap-2">
                        <Dialog>
                          <DialogTrigger asChild>
                            <Button variant="outline" size="sm">
                              <Eye className="h-4 w-4" />
                            </Button>
                          </DialogTrigger>
                          <DialogContent className="max-w-2xl">
                            <DialogHeader>
                              <DialogTitle>Order Details - {order.id}</DialogTitle>
                              <DialogDescription>
                                Complete order information and management options
                              </DialogDescription>
                            </DialogHeader>
                            <div className="space-y-4">
                              <div className="grid grid-cols-2 gap-4">
                                <div>
                                  <label className="text-sm font-medium">Client Name</label>
                                  <p className="text-sm text-muted-foreground">{order.clientName}</p>
                                </div>
                                <div>
                                  <label className="text-sm font-medium">Order Date</label>
                                  <p className="text-sm text-muted-foreground">{order.orderDate}</p>
                                </div>
                              </div>
                              <div>
                                <label className="text-sm font-medium">Items Ordered</label>
                                <ul className="mt-2 space-y-1">
                                  {order.items.map((item, index) => (
                                    <li key={index} className="text-sm text-muted-foreground">• {item}</li>
                                  ))}
                                </ul>
                              </div>
                              <div className="flex gap-2 mt-4">
                                <Button variant="outline">Edit Order</Button>
                                <Button variant="outline">Send Invoice</Button>
                                <Button>Mark as Delivered</Button>
                              </div>
                            </div>
                          </DialogContent>
                        </Dialog>
                        <Button variant="outline" size="sm">
                          <Download className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>
      </div>
    </DashboardLayout>
  );
}