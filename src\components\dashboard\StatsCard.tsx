import { ReactNode } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>it<PERSON> } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { LucideIcon } from "lucide-react";

interface StatsCardProps {
  title: string;
  value: string | number;
  change?: {
    value: string;
    type: "increase" | "decrease" | "neutral";
  };
  icon: LucideIcon;
  description?: string;
  children?: ReactNode;
}

export function StatsCard({ 
  title, 
  value, 
  change, 
  icon: Icon, 
  description,
  children 
}: StatsCardProps) {
  const changeColor = {
    increase: "text-success",
    decrease: "text-destructive", 
    neutral: "text-muted-foreground"
  };

  return (
    <Card className="relative overflow-hidden shadow-card hover:shadow-primary transition-smooth border-border">
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium text-muted-foreground">
          {title}
        </CardTitle>
        <div className="p-2 bg-gradient-secondary rounded-lg">
          <Icon className="h-4 w-4 text-primary" />
        </div>
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold text-foreground">{value}</div>
        <div className="flex items-center gap-2 mt-1">
          {change && (
            <Badge 
              variant="outline" 
              className={`${changeColor[change.type]} border-transparent bg-muted/50`}
            >
              {change.value}
            </Badge>
          )}
          {description && (
            <p className="text-xs text-muted-foreground">{description}</p>
          )}
        </div>
        {children}
      </CardContent>
    </Card>
  );
}