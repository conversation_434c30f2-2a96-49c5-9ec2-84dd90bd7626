import { DashboardLayout } from "@/components/layout/DashboardLayout";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { 
  Plus,
  Search,
  Filter,
  User,
  Mail,
  Phone,
  Calendar,
  CreditCard,
  MoreVertical,
  Eye,
  Edit,
  MessageSquare,
  Star
} from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

const clients = [
  {
    id: 1,
    name: "<PERSON> <PERSON>",
    email: "<EMAIL>",
    phone: "+91 98765 43210",
    type: "Wedding",
    status: "active",
    events: 1,
    totalSpent: "₹85,000",
    lastEvent: "Dec 15, 2024",
    rating: 5,
    avatar: "/placeholder-avatar.jpg",
    location: "Mumbai",
    referralSource: "Instagram"
  },
  {
    id: 2,
    name: "TechCorp Industries",
    email: "<EMAIL>",
    phone: "+91 99887 76543",
    type: "Corporate",
    status: "active",
    events: 3,
    totalSpent: "₹4,50,000",
    lastEvent: "Dec 18, 2024",
    rating: 4,
    avatar: "/placeholder-avatar.jpg",
    location: "Bangalore",
    referralSource: "LinkedIn"
  },
  {
    id: 3,
    name: "Emma Rodriguez",
    email: "<EMAIL>",
    phone: "+91 87654 32109",
    type: "Birthday",
    status: "prospect",
    events: 0,
    totalSpent: "₹0",
    lastEvent: "—",
    rating: 0,
    avatar: "/placeholder-avatar.jpg",
    location: "Delhi",
    referralSource: "Referral"
  },
  {
    id: 4,
    name: "Fashion Forward Ltd",
    email: "<EMAIL>",
    phone: "+91 76543 21098",
    type: "Fashion",
    status: "completed",
    events: 2,
    totalSpent: "₹3,20,000",
    lastEvent: "Nov 28, 2024",
    rating: 5,
    avatar: "/placeholder-avatar.jpg",
    location: "Mumbai",
    referralSource: "Website"
  },
  {
    id: 5,
    name: "Mike & Lisa Thompson",
    email: "<EMAIL>",
    phone: "+91 65432 10987",
    type: "Anniversary",
    status: "completed",
    events: 1,
    totalSpent: "₹45,000",
    lastEvent: "Nov 15, 2024",
    rating: 5,
    avatar: "/placeholder-avatar.jpg",
    location: "Goa",
    referralSource: "Google"
  },
  {
    id: 6,
    name: "Startup Hub",
    email: "<EMAIL>",
    phone: "+91 54321 09876",
    type: "Corporate",
    status: "prospect",
    events: 0,
    totalSpent: "₹0",
    lastEvent: "—",
    rating: 0,
    avatar: "/placeholder-avatar.jpg",
    location: "Pune",
    referralSource: "Cold Call"
  }
];

const statusStyles = {
  active: "bg-success/10 text-success border-success/20",
  prospect: "bg-warning/10 text-warning border-warning/20",
  completed: "bg-muted/10 text-muted-foreground border-muted/20",
  inactive: "bg-destructive/10 text-destructive border-destructive/20"
};

const typeStyles = {
  Wedding: "bg-pink-500/10 text-pink-600 border-pink-500/20",
  Corporate: "bg-blue-500/10 text-blue-600 border-blue-500/20",
  Birthday: "bg-purple-500/10 text-purple-600 border-purple-500/20",
  Fashion: "bg-orange-500/10 text-orange-600 border-orange-500/20",
  Anniversary: "bg-red-500/10 text-red-600 border-red-500/20"
};

export default function Clients() {
  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row justify-between items-start gap-4">
          <div>
            <h1 className="text-3xl font-bold bg-gradient-primary bg-clip-text text-transparent">
              Clients & CRM
            </h1>
            <p className="text-muted-foreground">
              Manage your client relationships and track opportunities
            </p>
          </div>
          <Button variant="gradient" className="shrink-0">
            <Plus className="h-4 w-4 mr-2" />
            Add Client
          </Button>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card className="shadow-card border-border">
            <CardContent className="p-4 text-center">
              <div className="text-2xl font-bold text-foreground">24</div>
              <div className="text-sm text-muted-foreground">Total Clients</div>
            </CardContent>
          </Card>
          <Card className="shadow-card border-border">
            <CardContent className="p-4 text-center">
              <div className="text-2xl font-bold text-success">8</div>
              <div className="text-sm text-muted-foreground">Active Projects</div>
            </CardContent>
          </Card>
          <Card className="shadow-card border-border">
            <CardContent className="p-4 text-center">
              <div className="text-2xl font-bold text-warning">5</div>
              <div className="text-sm text-muted-foreground">Prospects</div>
            </CardContent>
          </Card>
          <Card className="shadow-card border-border">
            <CardContent className="p-4 text-center">
              <div className="text-2xl font-bold text-primary">₹12.5L</div>
              <div className="text-sm text-muted-foreground">Total Revenue</div>
            </CardContent>
          </Card>
        </div>

        {/* Filters */}
        <Card className="shadow-card border-border">
          <CardContent className="p-4">
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search clients..."
                  className="pl-10"
                />
              </div>
              <Select>
                <SelectTrigger className="w-full sm:w-[180px]">
                  <SelectValue placeholder="Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="active">Active</SelectItem>
                  <SelectItem value="prospect">Prospect</SelectItem>
                  <SelectItem value="completed">Completed</SelectItem>
                  <SelectItem value="inactive">Inactive</SelectItem>
                </SelectContent>
              </Select>
              <Select>
                <SelectTrigger className="w-full sm:w-[180px]">
                  <SelectValue placeholder="Type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Types</SelectItem>
                  <SelectItem value="wedding">Wedding</SelectItem>
                  <SelectItem value="corporate">Corporate</SelectItem>
                  <SelectItem value="birthday">Birthday</SelectItem>
                  <SelectItem value="fashion">Fashion</SelectItem>
                </SelectContent>
              </Select>
              <Button variant="outline">
                <Filter className="h-4 w-4 mr-2" />
                Filters
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Clients List */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {clients.map((client) => (
            <Card key={client.id} className="shadow-card border-border hover:shadow-primary transition-smooth">
              <CardContent className="p-6">
                <div className="flex items-start justify-between mb-4">
                  <div className="flex items-center gap-3">
                    <Avatar className="h-12 w-12">
                      <AvatarImage src={client.avatar} alt={client.name} />
                      <AvatarFallback className="bg-gradient-primary text-white">
                        <User className="h-6 w-6" />
                      </AvatarFallback>
                    </Avatar>
                    <div>
                      <h3 className="font-semibold text-foreground">{client.name}</h3>
                      <div className="flex items-center gap-2 mt-1">
                        <Badge 
                          variant="outline" 
                          className={statusStyles[client.status as keyof typeof statusStyles]}
                        >
                          {client.status}
                        </Badge>
                        <Badge 
                          variant="outline" 
                          className={typeStyles[client.type as keyof typeof typeStyles]}
                        >
                          {client.type}
                        </Badge>
                      </div>
                    </div>
                  </div>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="icon">
                        <MoreVertical className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem>
                        <Eye className="h-4 w-4 mr-2" />
                        View Profile
                      </DropdownMenuItem>
                      <DropdownMenuItem>
                        <Edit className="h-4 w-4 mr-2" />
                        Edit Client
                      </DropdownMenuItem>
                      <DropdownMenuItem>
                        <MessageSquare className="h-4 w-4 mr-2" />
                        Send Message
                      </DropdownMenuItem>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem>
                        <CreditCard className="h-4 w-4 mr-2" />
                        Create Invoice
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>

                <div className="space-y-2 mb-4">
                  <div className="flex items-center gap-2 text-sm text-muted-foreground">
                    <Mail className="h-4 w-4" />
                    {client.email}
                  </div>
                  <div className="flex items-center gap-2 text-sm text-muted-foreground">
                    <Phone className="h-4 w-4" />
                    {client.phone}
                  </div>
                  <div className="flex items-center gap-2 text-sm text-muted-foreground">
                    <Calendar className="h-4 w-4" />
                    Last event: {client.lastEvent}
                  </div>
                </div>

                <div className="grid grid-cols-3 gap-4 py-3 border-t border-border">
                  <div className="text-center">
                    <div className="text-lg font-semibold text-foreground">{client.events}</div>
                    <div className="text-xs text-muted-foreground">Events</div>
                  </div>
                  <div className="text-center">
                    <div className="text-lg font-semibold text-foreground">{client.totalSpent}</div>
                    <div className="text-xs text-muted-foreground">Revenue</div>
                  </div>
                  <div className="text-center">
                    <div className="flex items-center justify-center gap-1">
                      {[...Array(5)].map((_, i) => (
                        <Star
                          key={i}
                          className={`h-3 w-3 ${
                            i < client.rating 
                              ? "text-yellow-500 fill-current" 
                              : "text-muted-foreground"
                          }`}
                        />
                      ))}
                    </div>
                    <div className="text-xs text-muted-foreground">Rating</div>
                  </div>
                </div>

                <div className="flex items-center justify-between pt-3 border-t border-border">
                  <div className="text-sm text-muted-foreground">
                    Source: {client.referralSource}
                  </div>
                  <div className="text-sm text-muted-foreground">
                    📍 {client.location}
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </DashboardLayout>
  );
}