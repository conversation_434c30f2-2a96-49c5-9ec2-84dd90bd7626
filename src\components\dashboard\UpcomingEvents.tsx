import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { 
  Calendar,
  MapPin,
  Clock,
  Users,
  Camera,
  ArrowRight,
  Star
} from "lucide-react";

const events = [
  {
    id: 1,
    title: "Sarah & John Wedding",
    date: "Dec 15, 2024",
    time: "4:00 PM",
    location: "Grand Ballroom, Hotel Paradise",
    type: "Wedding",
    status: "confirmed",
    priority: "high",
    guests: 250,
    photographer: "Alex Studio",
    packages: ["Photography", "Videography", "Album"],
    progress: 85
  },
  {
    id: 2,
    title: "TechCorp Annual Meet",
    date: "Dec 18, 2024", 
    time: "10:00 AM",
    location: "Convention Center",
    type: "Corporate",
    status: "confirmed",
    priority: "medium",
    guests: 500,
    photographer: "Mike Photo",
    packages: ["Event Coverage", "Portraits"],
    progress: 60
  },
  {
    id: 3,
    title: "<PERSON>'s Birthday Bash",
    date: "Dec 22, 2024",
    time: "6:00 PM", 
    location: "Private Villa",
    type: "Birthday",
    status: "pending",
    priority: "low",
    guests: 50,
    photographer: "Lisa Creative",
    packages: ["Party Coverage"],
    progress: 30
  },
  {
    id: 4,
    title: "Product Launch Event",
    date: "Dec 28, 2024",
    time: "2:00 PM",
    location: "Innovation Hub",
    type: "Corporate", 
    status: "draft",
    priority: "high",
    guests: 150,
    photographer: "Pro Studios",
    packages: ["Event Photography", "Product Shots"],
    progress: 15
  }
];

const statusStyles = {
  confirmed: "bg-success/10 text-success border-success/20",
  pending: "bg-warning/10 text-warning border-warning/20",
  draft: "bg-muted/10 text-muted-foreground border-muted/20"
};

const priorityStyles = {
  high: "bg-destructive/10 text-destructive border-destructive/20",
  medium: "bg-warning/10 text-warning border-warning/20", 
  low: "bg-success/10 text-success border-success/20"
};

const typeStyles = {
  Wedding: "bg-pink-500/10 text-pink-600 border-pink-500/20",
  Corporate: "bg-blue-500/10 text-blue-600 border-blue-500/20",
  Birthday: "bg-purple-500/10 text-purple-600 border-purple-500/20"
};

export function UpcomingEvents() {
  return (
    <Card className="shadow-card border-border">
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle className="text-lg font-semibold flex items-center gap-2">
          <Calendar className="h-5 w-5 text-primary" />
          Upcoming Events
        </CardTitle>
        <Button variant="outline" size="sm">
          View All
          <ArrowRight className="h-4 w-4 ml-1" />
        </Button>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {events.map((event) => (
            <div
              key={event.id}
              className="p-4 rounded-lg border border-border hover:shadow-primary transition-smooth cursor-pointer bg-card"
            >
              <div className="flex items-start justify-between mb-3">
                <div>
                  <h3 className="font-semibold text-foreground mb-1">
                    {event.title}
                  </h3>
                  <div className="flex items-center gap-4 text-sm text-muted-foreground mb-2">
                    <div className="flex items-center gap-1">
                      <Calendar className="h-4 w-4" />
                      {event.date}
                    </div>
                    <div className="flex items-center gap-1">
                      <Clock className="h-4 w-4" />
                      {event.time}
                    </div>
                  </div>
                  <div className="flex items-center gap-1 text-sm text-muted-foreground">
                    <MapPin className="h-4 w-4" />
                    {event.location}
                  </div>
                </div>
                <div className="flex flex-col gap-2">
                  <Badge 
                    variant="outline" 
                    className={statusStyles[event.status as keyof typeof statusStyles]}
                  >
                    {event.status}
                  </Badge>
                  <Badge 
                    variant="outline" 
                    className={priorityStyles[event.priority as keyof typeof priorityStyles]}
                  >
                    {event.priority}
                  </Badge>
                </div>
              </div>

              <div className="flex items-center gap-4 mb-3">
                <Badge 
                  variant="outline" 
                  className={typeStyles[event.type as keyof typeof typeStyles]}
                >
                  {event.type}
                </Badge>
                <div className="flex items-center gap-1 text-sm text-muted-foreground">
                  <Users className="h-4 w-4" />
                  {event.guests} guests
                </div>
                <div className="flex items-center gap-1 text-sm text-muted-foreground">
                  <Camera className="h-4 w-4" />
                  {event.photographer}
                </div>
              </div>

              <div className="flex items-center justify-between">
                <div className="flex flex-wrap gap-1">
                  {event.packages.map((pkg, index) => (
                    <Badge key={index} variant="secondary" className="text-xs">
                      {pkg}
                    </Badge>
                  ))}
                </div>
                <div className="flex items-center gap-2">
                  <div className="text-sm text-muted-foreground">
                    {event.progress}% ready
                  </div>
                  <div className="w-16 h-2 bg-muted rounded-full">
                    <div 
                      className="h-full bg-gradient-primary rounded-full transition-smooth"
                      style={{ width: `${event.progress}%` }}
                    />
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}