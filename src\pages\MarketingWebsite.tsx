import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Camera, Phone, Mail, MapPin, Clock, Star, Heart, Users, Award, ArrowRight, Menu, X, User } from "lucide-react";
import { useState } from "react";
import ShopSection from "@/components/website/ShopSection";
import BlogSection from "@/components/website/BlogSection";
import { Logo } from "@/components/ui/logo";

export default function MarketingWebsite() {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);

  return (
    <div className="min-h-screen bg-background">
      {/* Navigation */}
      <nav className="bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 border-b border-border sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <Logo size="md" />
            
            {/* Desktop Menu */}
            <div className="hidden md:flex items-center space-x-8">
              <a href="#home" className="text-foreground hover:text-primary transition-colors">Home</a>
              <a href="#portfolio" className="text-foreground hover:text-primary transition-colors">Portfolio</a>
              <a href="#services" className="text-foreground hover:text-primary transition-colors">Services</a>
              <a href="#packages" className="text-foreground hover:text-primary transition-colors">Packages</a>
              <a href="#about" className="text-foreground hover:text-primary transition-colors">About</a>
              <a href="#contact" className="text-foreground hover:text-primary transition-colors">Contact</a>
              <a href="#shop" className="text-foreground hover:text-primary transition-colors">Shop</a>
              <a href="#blog" className="text-foreground hover:text-primary transition-colors">Blog</a>
              <Button variant="outline" onClick={() => window.location.href = '/login'} className="gap-2">
                Admin Login
              </Button>
              <Button className="gap-2">
                <Phone className="h-4 w-4" />
                Book Now
              </Button>
            </div>

            {/* Mobile Menu Button */}
            <button
              type="button"
              onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
              className="md:hidden p-2"
            >
              {mobileMenuOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
            </button>
          </div>

          {/* Mobile Menu */}
          {mobileMenuOpen && (
            <div className="md:hidden py-4 border-t border-border">
              <div className="flex flex-col space-y-4">
                <a href="#home" className="text-foreground hover:text-primary transition-colors">Home</a>
                <a href="#portfolio" className="text-foreground hover:text-primary transition-colors">Portfolio</a>
                <a href="#services" className="text-foreground hover:text-primary transition-colors">Services</a>
                <a href="#packages" className="text-foreground hover:text-primary transition-colors">Packages</a>
                <a href="#about" className="text-foreground hover:text-primary transition-colors">About</a>
                <a href="#contact" className="text-foreground hover:text-primary transition-colors">Contact</a>
                <a href="#shop" className="text-foreground hover:text-primary transition-colors">Shop</a>
                <a href="#blog" className="text-foreground hover:text-primary transition-colors">Blog</a>
                <Button variant="outline" onClick={() => window.location.href = '/login'} className="gap-2 w-fit">
                  Admin Login
                </Button>
                <Button className="gap-2 w-fit">
                  <Phone className="h-4 w-4" />
                  Book Now
                </Button>
              </div>
            </div>
          )}
        </div>
      </nav>

      {/* Hero Section */}
      <section id="home" className="py-20 bg-gradient-to-r from-primary/10 to-primary/5">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div className="space-y-6">
              <Badge className="w-fit">Professional Photography Services</Badge>
              <h1 className="text-5xl lg:text-6xl font-bold text-foreground leading-tight">
                Capture Your 
                <span className="text-primary"> Special Moments</span>
              </h1>
              <p className="text-xl text-muted-foreground leading-relaxed">
                Professional wedding, corporate, and event photography services in Bardoli, Gujarat.
                Located at Lotus Mall, Shivaji Circle - Creating timeless memories with artistic excellence.
              </p>
              <div className="flex flex-col sm:flex-row gap-4">
                <Button size="lg" className="gap-2">
                  <Phone className="h-5 w-5" />
                  Book Your Session
                </Button>
                <Button size="lg" variant="outline" className="gap-2">
                  <Camera className="h-5 w-5" />
                  View Portfolio
                </Button>
              </div>
              <div className="flex items-center gap-6 pt-4">
                <div className="flex items-center gap-2">
                  <div className="flex">
                    {[...Array(5)].map((_, i) => (
                      <Star key={i} className="h-5 w-5 fill-yellow-400 text-yellow-400" />
                    ))}
                  </div>
                  <span className="text-sm text-muted-foreground">4.532 ⭐ Google Reviews</span>
                </div>
              </div>
            </div>
            <div className="relative">
              <img 
                src="/src/assets/studio-hero.jpg" 
                alt="Professional Photography" 
                className="rounded-2xl shadow-card w-full h-[600px] object-cover"
              />
              <div className="absolute -bottom-6 -left-6 bg-card border border-border rounded-xl p-4 shadow-card">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-primary/10 rounded-lg">
                    <Award className="h-6 w-6 text-primary" />
                  </div>
                  <div>
                    <p className="font-semibold">Award Winning</p>
                    <p className="text-sm text-muted-foreground">Photography Studio</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Services Section */}
      <section id="services" className="py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <Badge className="mb-4">Our Services</Badge>
            <h2 className="text-4xl font-bold text-foreground mb-4">Professional Photography Services</h2>
            <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
              From intimate weddings to grand corporate events, we capture every moment with precision and artistry
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {/* Wedding Photography */}
            <Card className="group hover:shadow-primary transition-all duration-300">
              <CardContent className="p-6">
                <div className="mb-4">
                  <div className="p-3 bg-primary/10 rounded-lg w-fit mb-4">
                    <Heart className="h-8 w-8 text-primary" />
                  </div>
                  <h3 className="text-xl font-semibold mb-2">Wedding Photography</h3>
                  <p className="text-muted-foreground mb-4">
                    Complete wedding coverage including pre-wedding, ceremony, and reception photography
                  </p>
                  <div className="space-y-2 mb-6">
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 bg-primary rounded-full"></div>
                      <span className="text-sm">Pre-wedding shoots</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 bg-primary rounded-full"></div>
                      <span className="text-sm">Ceremony photography</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 bg-primary rounded-full"></div>
                      <span className="text-sm">Reception coverage</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 bg-primary rounded-full"></div>
                      <span className="text-sm">Professional editing</span>
                    </div>
                  </div>
                  <div className="space-y-1 mb-4">
                    <div className="text-2xl font-bold text-primary">₹25,000 - ₹75,000</div>
                    <p className="text-sm text-muted-foreground">Starting packages available</p>
                  </div>
                  <Button className="w-full gap-2 group-hover:bg-primary group-hover:text-primary-foreground">
                    Get Quote <ArrowRight className="h-4 w-4" />
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* Corporate Events */}
            <Card className="group hover:shadow-primary transition-all duration-300">
              <CardContent className="p-6">
                <div className="mb-4">
                  <div className="p-3 bg-primary/10 rounded-lg w-fit mb-4">
                    <Users className="h-8 w-8 text-primary" />
                  </div>
                  <h3 className="text-xl font-semibold mb-2">Corporate Events</h3>
                  <p className="text-muted-foreground mb-4">
                    Professional corporate event photography for conferences, launches, and meetings
                  </p>
                  <div className="space-y-2 mb-6">
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 bg-primary rounded-full"></div>
                      <span className="text-sm">Conference photography</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 bg-primary rounded-full"></div>
                      <span className="text-sm">Product launches</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 bg-primary rounded-full"></div>
                      <span className="text-sm">Team events</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 bg-primary rounded-full"></div>
                      <span className="text-sm">Same-day delivery</span>
                    </div>
                  </div>
                  <div className="space-y-1 mb-4">
                    <div className="text-2xl font-bold text-primary">₹15,000 - ₹45,000</div>
                    <p className="text-sm text-muted-foreground">Half day to full day coverage</p>
                  </div>
                  <Button className="w-full gap-2 group-hover:bg-primary group-hover:text-primary-foreground">
                    Get Quote <ArrowRight className="h-4 w-4" />
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* Special Events */}
            <Card className="group hover:shadow-primary transition-all duration-300">
              <CardContent className="p-6">
                <div className="mb-4">
                  <div className="p-3 bg-primary/10 rounded-lg w-fit mb-4">
                    <Camera className="h-8 w-8 text-primary" />
                  </div>
                  <h3 className="text-xl font-semibold mb-2">Special Events</h3>
                  <p className="text-muted-foreground mb-4">
                    Birthday parties, anniversaries, and personal celebrations photography
                  </p>
                  <div className="space-y-2 mb-6">
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 bg-primary rounded-full"></div>
                      <span className="text-sm">Birthday parties</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 bg-primary rounded-full"></div>
                      <span className="text-sm">Anniversary shoots</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 bg-primary rounded-full"></div>
                      <span className="text-sm">Family portraits</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 bg-primary rounded-full"></div>
                      <span className="text-sm">Custom packages</span>
                    </div>
                  </div>
                  <div className="space-y-1 mb-4">
                    <div className="text-2xl font-bold text-primary">₹8,000 - ₹25,000</div>
                    <p className="text-sm text-muted-foreground">2-4 hours coverage</p>
                  </div>
                  <Button className="w-full gap-2 group-hover:bg-primary group-hover:text-primary-foreground">
                    Get Quote <ArrowRight className="h-4 w-4" />
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Photography Packages */}
      <section id="packages" className="py-20 bg-muted/30">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <Badge className="mb-4">Photography Packages</Badge>
            <h2 className="text-4xl font-bold text-foreground mb-4">Choose Your Perfect Package</h2>
            <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
              Tailored photography packages to suit every occasion and budget
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {/* Basic Package */}
            <Card className="group hover:shadow-primary transition-all duration-300 relative">
              <CardContent className="p-6">
                <div className="text-center mb-6">
                  <h3 className="text-2xl font-bold mb-2">Basic Package</h3>
                  <div className="text-4xl font-bold text-primary mb-2">₹8,000</div>
                  <p className="text-muted-foreground">Perfect for small events</p>
                </div>
                <div className="space-y-3 mb-6">
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-primary rounded-full"></div>
                    <span className="text-sm">2-3 hours coverage</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-primary rounded-full"></div>
                    <span className="text-sm">50+ edited photos</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-primary rounded-full"></div>
                    <span className="text-sm">Digital gallery</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-primary rounded-full"></div>
                    <span className="text-sm">Basic editing</span>
                  </div>
                </div>
                <Button className="w-full gap-2">
                  Choose Package <ArrowRight className="h-4 w-4" />
                </Button>
              </CardContent>
            </Card>

            {/* Premium Package */}
            <Card className="group hover:shadow-primary transition-all duration-300 relative border-primary">
              <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                <Badge className="bg-primary text-primary-foreground">Most Popular</Badge>
              </div>
              <CardContent className="p-6">
                <div className="text-center mb-6">
                  <h3 className="text-2xl font-bold mb-2">Premium Package</h3>
                  <div className="text-4xl font-bold text-primary mb-2">₹25,000</div>
                  <p className="text-muted-foreground">Ideal for weddings & events</p>
                </div>
                <div className="space-y-3 mb-6">
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-primary rounded-full"></div>
                    <span className="text-sm">6-8 hours coverage</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-primary rounded-full"></div>
                    <span className="text-sm">200+ edited photos</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-primary rounded-full"></div>
                    <span className="text-sm">Premium digital gallery</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-primary rounded-full"></div>
                    <span className="text-sm">Professional editing</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-primary rounded-full"></div>
                    <span className="text-sm">Same day highlights</span>
                  </div>
                </div>
                <Button className="w-full gap-2">
                  Choose Package <ArrowRight className="h-4 w-4" />
                </Button>
              </CardContent>
            </Card>

            {/* Luxury Package */}
            <Card className="group hover:shadow-primary transition-all duration-300 relative">
              <CardContent className="p-6">
                <div className="text-center mb-6">
                  <h3 className="text-2xl font-bold mb-2">Luxury Package</h3>
                  <div className="text-4xl font-bold text-primary mb-2">₹50,000</div>
                  <p className="text-muted-foreground">Complete coverage solution</p>
                </div>
                <div className="space-y-3 mb-6">
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-primary rounded-full"></div>
                    <span className="text-sm">Full day coverage</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-primary rounded-full"></div>
                    <span className="text-sm">500+ edited photos</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-primary rounded-full"></div>
                    <span className="text-sm">Premium album included</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-primary rounded-full"></div>
                    <span className="text-sm">Cinematic editing</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-primary rounded-full"></div>
                    <span className="text-sm">Multiple photographers</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-primary rounded-full"></div>
                    <span className="text-sm">Drone photography</span>
                  </div>
                </div>
                <Button className="w-full gap-2">
                  Choose Package <ArrowRight className="h-4 w-4" />
                </Button>
              </CardContent>
            </Card>
          </div>

          <div className="text-center mt-12">
            <p className="text-muted-foreground mb-4">
              Need a custom package? We can create a personalized solution for your specific needs.
            </p>
            <Button variant="outline" size="lg" className="gap-2">
              Request Custom Quote <ArrowRight className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </section>

      {/* Portfolio Preview */}
      <section id="portfolio" className="py-20 bg-background">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <Badge className="mb-4">Our Work</Badge>
            <h2 className="text-4xl font-bold text-foreground mb-4">Photography Portfolio</h2>
            <p className="text-xl text-muted-foreground">
              Showcasing our finest work across different photography styles
            </p>
          </div>

          {/* Portfolio Categories */}
          <div className="flex flex-wrap justify-center gap-4 mb-12">
            <Button variant="default" size="sm">All</Button>
            <Button variant="outline" size="sm">Weddings</Button>
            <Button variant="outline" size="sm">Corporate</Button>
            <Button variant="outline" size="sm">Portraits</Button>
            <Button variant="outline" size="sm">Events</Button>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
            {/* Wedding Photos */}
            <div className="group relative overflow-hidden rounded-xl">
              <img
                src="/src/assets/studio-hero.jpg"
                alt="Wedding Photography"
                className="w-full h-80 object-cover group-hover:scale-105 transition-transform duration-300"
              />
              <div className="absolute inset-0 bg-black/40 group-hover:bg-black/20 transition-colors duration-300" />
              <div className="absolute bottom-4 left-4 text-white">
                <Badge className="mb-2 bg-white/20 text-white border-white/30">Wedding</Badge>
                <p className="font-semibold">Priya & Raj Wedding</p>
                <p className="text-sm opacity-90">Bardoli, Gujarat</p>
              </div>
            </div>

            <div className="group relative overflow-hidden rounded-xl">
              <img
                src="/src/assets/event-collage.jpg"
                alt="Corporate Event"
                className="w-full h-80 object-cover group-hover:scale-105 transition-transform duration-300"
              />
              <div className="absolute inset-0 bg-black/40 group-hover:bg-black/20 transition-colors duration-300" />
              <div className="absolute bottom-4 left-4 text-white">
                <Badge className="mb-2 bg-white/20 text-white border-white/30">Corporate</Badge>
                <p className="font-semibold">TechCorp Annual Meet</p>
                <p className="text-sm opacity-90">Surat, Gujarat</p>
              </div>
            </div>

            <div className="group relative overflow-hidden rounded-xl">
              <img
                src="/src/assets/studio-hero.jpg"
                alt="Portrait Photography"
                className="w-full h-80 object-cover group-hover:scale-105 transition-transform duration-300"
              />
              <div className="absolute inset-0 bg-black/40 group-hover:bg-black/20 transition-colors duration-300" />
              <div className="absolute bottom-4 left-4 text-white">
                <Badge className="mb-2 bg-white/20 text-white border-white/30">Portrait</Badge>
                <p className="font-semibold">Family Portrait Session</p>
                <p className="text-sm opacity-90">Bardoli, Gujarat</p>
              </div>
            </div>

            <div className="group relative overflow-hidden rounded-xl">
              <img
                src="/src/assets/event-collage.jpg"
                alt="Birthday Party"
                className="w-full h-80 object-cover group-hover:scale-105 transition-transform duration-300"
              />
              <div className="absolute inset-0 bg-black/40 group-hover:bg-black/20 transition-colors duration-300" />
              <div className="absolute bottom-4 left-4 text-white">
                <Badge className="mb-2 bg-white/20 text-white border-white/30">Event</Badge>
                <p className="font-semibold">Birthday Celebration</p>
                <p className="text-sm opacity-90">Bardoli, Gujarat</p>
              </div>
            </div>

            <div className="group relative overflow-hidden rounded-xl">
              <img
                src="/src/assets/studio-hero.jpg"
                alt="Engagement Photography"
                className="w-full h-80 object-cover group-hover:scale-105 transition-transform duration-300"
              />
              <div className="absolute inset-0 bg-black/40 group-hover:bg-black/20 transition-colors duration-300" />
              <div className="absolute bottom-4 left-4 text-white">
                <Badge className="mb-2 bg-white/20 text-white border-white/30">Wedding</Badge>
                <p className="font-semibold">Engagement Session</p>
                <p className="text-sm opacity-90">Bardoli, Gujarat</p>
              </div>
            </div>

            <div className="group relative overflow-hidden rounded-xl">
              <img
                src="/src/assets/event-collage.jpg"
                alt="Corporate Headshots"
                className="w-full h-80 object-cover group-hover:scale-105 transition-transform duration-300"
              />
              <div className="absolute inset-0 bg-black/40 group-hover:bg-black/20 transition-colors duration-300" />
              <div className="absolute bottom-4 left-4 text-white">
                <Badge className="mb-2 bg-white/20 text-white border-white/30">Corporate</Badge>
                <p className="font-semibold">Professional Headshots</p>
                <p className="text-sm opacity-90">Surat, Gujarat</p>
              </div>
            </div>
          </div>

          <div className="text-center mt-12">
            <Button variant="outline" size="lg" className="gap-2">
              View Full Portfolio <ArrowRight className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </section>

      {/* Google Reviews Section */}
      <section className="py-20 bg-muted/30">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <Badge className="mb-4">Client Reviews</Badge>
            <h2 className="text-4xl font-bold text-foreground mb-4">What Our Clients Say</h2>
            <div className="flex items-center justify-center gap-4 mb-6">
              <div className="flex">
                {[...Array(5)].map((_, i) => (
                  <Star key={i} className="h-8 w-8 fill-yellow-400 text-yellow-400" />
                ))}
              </div>
              <div className="text-3xl font-bold text-primary">4.532</div>
              <div className="text-muted-foreground">
                <p className="font-semibold">Google Reviews</p>
                <p className="text-sm">Based on 500+ reviews</p>
              </div>
            </div>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {/* Review 1 */}
            <Card className="p-6">
              <CardContent className="p-0">
                <div className="flex items-center gap-3 mb-4">
                  <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center">
                    <User className="h-6 w-6 text-primary" />
                  </div>
                  <div>
                    <p className="font-semibold">Priya Patel</p>
                    <div className="flex">
                      {[...Array(5)].map((_, i) => (
                        <Star key={i} className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                      ))}
                    </div>
                  </div>
                </div>
                <p className="text-muted-foreground mb-3">
                  "Absolutely amazing work! Om Digital Studio captured our wedding perfectly.
                  Every moment was beautifully documented. Highly recommended!"
                </p>
                <p className="text-sm text-muted-foreground">2 weeks ago</p>
              </CardContent>
            </Card>

            {/* Review 2 */}
            <Card className="p-6">
              <CardContent className="p-0">
                <div className="flex items-center gap-3 mb-4">
                  <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center">
                    <User className="h-6 w-6 text-primary" />
                  </div>
                  <div>
                    <p className="font-semibold">Rajesh Shah</p>
                    <div className="flex">
                      {[...Array(5)].map((_, i) => (
                        <Star key={i} className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                      ))}
                    </div>
                  </div>
                </div>
                <p className="text-muted-foreground mb-3">
                  "Professional service for our corporate event. The team was punctual,
                  creative, and delivered exceptional quality photos."
                </p>
                <p className="text-sm text-muted-foreground">1 month ago</p>
              </CardContent>
            </Card>

            {/* Review 3 */}
            <Card className="p-6">
              <CardContent className="p-0">
                <div className="flex items-center gap-3 mb-4">
                  <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center">
                    <User className="h-6 w-6 text-primary" />
                  </div>
                  <div>
                    <p className="font-semibold">Meera Desai</p>
                    <div className="flex">
                      {[...Array(5)].map((_, i) => (
                        <Star key={i} className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                      ))}
                    </div>
                  </div>
                </div>
                <p className="text-muted-foreground mb-3">
                  "Best photography studio in Bardoli! They made our family portrait session
                  so comfortable and the results were stunning."
                </p>
                <p className="text-sm text-muted-foreground">3 weeks ago</p>
              </CardContent>
            </Card>
          </div>

          <div className="text-center mt-12">
            <Button variant="outline" size="lg" className="gap-2">
              View All Reviews <ArrowRight className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </section>

      {/* About Section */}
      <section id="about" className="py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div>
              <Badge className="mb-4">About Us</Badge>
              <h2 className="text-4xl font-bold text-foreground mb-6">
                Professional Photography Studio in Bardoli
              </h2>
              <p className="text-lg text-muted-foreground mb-6">
                Om Digital Studio has been capturing precious moments for families and businesses 
                in Bardoli and surrounding areas for over 8 years. Our team of experienced photographers 
                specializes in creating timeless memories through artistic excellence.
              </p>
              <div className="space-y-4 mb-8">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-primary/10 rounded-lg">
                    <Award className="h-5 w-5 text-primary" />
                  </div>
                  <span>8+ Years of Experience</span>
                </div>
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-primary/10 rounded-lg">
                    <Users className="h-5 w-5 text-primary" />
                  </div>
                  <span>500+ Happy Clients</span>
                </div>
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-primary/10 rounded-lg">
                    <Camera className="h-5 w-5 text-primary" />
                  </div>
                  <span>Latest Professional Equipment</span>
                </div>
              </div>
              <Button size="lg" className="gap-2">
                Learn More <ArrowRight className="h-4 w-4" />
              </Button>
            </div>
            <div className="relative">
              <img 
                src="/src/assets/studio-hero.jpg" 
                alt="About Om Digital Studio" 
                className="rounded-2xl shadow-card w-full h-[500px] object-cover"
              />
            </div>
          </div>
        </div>
      </section>

      {/* Shop Section */}
      <ShopSection />

      {/* Blog Section */}
      <BlogSection />

      {/* Contact Section */}
      <section id="contact" className="py-20 bg-background">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <Badge className="mb-4">Contact Us</Badge>
            <h2 className="text-4xl font-bold text-foreground mb-4">Get In Touch</h2>
            <p className="text-xl text-muted-foreground">
              Ready to capture your special moments? Let's discuss your photography needs
            </p>
          </div>

          <div className="grid lg:grid-cols-2 gap-12">
            {/* Contact Information */}
            <div className="space-y-8">
              <div>
                <h3 className="text-2xl font-semibold mb-6">Visit Our Studio</h3>
                <div className="space-y-4">
                  <div className="flex items-start gap-3">
                    <MapPin className="h-5 w-5 text-primary mt-1 flex-shrink-0" />
                    <div>
                      <p className="font-medium">Studio Address</p>
                      <p className="text-muted-foreground">
                        Lotus Mall, Shivaji Circle, near Reliance Point<br />
                        Patel Nagar, Bardoli, Gujarat 394601
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center gap-3">
                    <Phone className="h-5 w-5 text-primary" />
                    <div>
                      <p className="font-medium">Phone</p>
                      <p className="text-muted-foreground">+91 99790 49677</p>
                    </div>
                  </div>
                  <div className="flex items-center gap-3">
                    <Mail className="h-5 w-5 text-primary" />
                    <div>
                      <p className="font-medium">Email</p>
                      <p className="text-muted-foreground"><EMAIL></p>
                    </div>
                  </div>
                  <div className="flex items-start gap-3">
                    <Clock className="h-5 w-5 text-primary mt-1" />
                    <div>
                      <p className="font-medium">Business Hours</p>
                      <p className="text-muted-foreground">
                        Monday - Sunday: 8:00 AM - 8:00 PM<br />
                        Open 7 days a week
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Contact Form */}
            <Card>
              <CardContent className="p-6">
                <h3 className="text-xl font-semibold mb-6">Send us a message</h3>
                <form className="space-y-4">
                  <div className="grid md:grid-cols-2 gap-4">
                    <div>
                      <label className="text-sm font-medium mb-2 block">First Name</label>
                      <Input placeholder="Your first name" />
                    </div>
                    <div>
                      <label className="text-sm font-medium mb-2 block">Last Name</label>
                      <Input placeholder="Your last name" />
                    </div>
                  </div>
                  <div>
                    <label className="text-sm font-medium mb-2 block">Email</label>
                    <Input type="email" placeholder="<EMAIL>" />
                  </div>
                  <div>
                    <label className="text-sm font-medium mb-2 block">Phone</label>
                    <Input placeholder="+91 Your phone number" />
                  </div>
                  <div>
                    <label className="text-sm font-medium mb-2 block">Event Type</label>
                    <Input placeholder="Wedding, Corporate Event, Birthday, etc." />
                  </div>
                  <div>
                    <label className="text-sm font-medium mb-2 block">Event Date</label>
                    <Input type="date" />
                  </div>
                  <div>
                    <label className="text-sm font-medium mb-2 block">Message</label>
                    <Textarea 
                      placeholder="Tell us about your event and photography requirements..."
                      className="min-h-[120px]"
                    />
                  </div>
                  <Button className="w-full gap-2">
                    <Mail className="h-4 w-4" />
                    Send Message
                  </Button>
                </form>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-card border-t border-border py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid md:grid-cols-4 gap-8">
            <div className="col-span-2">
              <Logo size="md" className="mb-4" />
              <p className="text-muted-foreground mb-4">
                Professional photography services in Bardoli, Gujarat. 
                Specializing in weddings, corporate events, and special occasions.
              </p>
              <div className="flex space-x-4">
                <div className="w-10 h-10 bg-primary/10 rounded-full flex items-center justify-center">
                  <span className="text-primary font-semibold">f</span>
                </div>
                <div className="w-10 h-10 bg-primary/10 rounded-full flex items-center justify-center">
                  <span className="text-primary font-semibold">@</span>
                </div>
                <div className="w-10 h-10 bg-primary/10 rounded-full flex items-center justify-center">
                  <span className="text-primary font-semibold">in</span>
                </div>
              </div>
            </div>
            
            <div>
              <h4 className="font-semibold mb-4">Services</h4>
              <ul className="space-y-2 text-muted-foreground">
                <li>Wedding Photography</li>
                <li>Corporate Events</li>
                <li>Birthday Parties</li>
                <li>Anniversary Shoots</li>
                <li>Family Portraits</li>
              </ul>
            </div>
            
            <div>
              <h4 className="font-semibold mb-4">Contact Info</h4>
              <ul className="space-y-2 text-muted-foreground">
                <li>+91 99790 49677</li>
                <li><EMAIL></li>
                <li>Lotus Mall, Shivaji Circle</li>
                <li>near Reliance Point, Patel Nagar</li>
                <li>Bardoli, Gujarat 394601</li>
              </ul>
            </div>
          </div>
          
          <div className="border-t border-border mt-8 pt-8 text-center text-muted-foreground">
            <p>&copy; 2024 Om Digital Studio. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  );
}