import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Textarea } from "@/components/ui/textarea";
import { 
  FileText, 
  Search, 
  Plus, 
  Eye, 
  Send,
  Download,
  Copy,
  CheckCircle,
  Clock,
  XCircle,
  DollarSign,
  Calendar,
  User
} from "lucide-react";
import { DashboardLayout } from "@/components/layout/DashboardLayout";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";

const quotes = [
  {
    id: "QUO-2024-001",
    clientName: "Amit & Sneha Wedding",
    contactEmail: "<EMAIL>",
    eventType: "Wedding",
    eventDate: "2024-03-15",
    quotedAmount: "₹85,000",
    status: "sent",
    validUntil: "2024-02-15",
    createdDate: "2024-01-15",
    services: ["Wedding Photography", "Videography", "Album", "Digital Gallery"]
  },
  {
    id: "QUO-2024-002", 
    clientName: "StartupXYZ Product Launch",
    contactEmail: "<EMAIL>",
    eventType: "Corporate Event",
    eventDate: "2024-02-20",
    quotedAmount: "₹45,000",
    status: "accepted",
    validUntil: "2024-02-10",
    createdDate: "2024-01-18",
    services: ["Event Photography", "Social Media Content", "Live Coverage"]
  },
  {
    id: "QUO-2024-003",
    clientName: "Baby Ananya Photoshoot", 
    contactEmail: "<EMAIL>",
    eventType: "Baby Shoot",
    eventDate: "2024-02-25",
    quotedAmount: "₹12,000",
    status: "draft",
    validUntil: "2024-02-20",
    createdDate: "2024-01-20",
    services: ["Baby Photography", "Family Portraits", "Digital Gallery"]
  },
  {
    id: "QUO-2024-004",
    clientName: "Fashion Brand - Spring Collection",
    contactEmail: "<EMAIL>",
    eventType: "Fashion Shoot",
    eventDate: "2024-03-01",
    quotedAmount: "₹35,000",
    status: "declined",
    validUntil: "2024-02-01",
    createdDate: "2024-01-12",
    services: ["Fashion Photography", "Product Shots", "Model Portfolio"]
  },
  {
    id: "QUO-2024-005",
    clientName: "Corporate Headshots - TechFirm",
    contactEmail: "<EMAIL>",
    eventType: "Corporate",
    eventDate: "2024-02-28",
    quotedAmount: "₹28,000",
    status: "follow_up",
    validUntil: "2024-02-25",
    createdDate: "2024-01-25",
    services: ["Professional Headshots", "Team Photos", "Office Photography"]
  }
];

const getStatusColor = (status: string) => {
  switch (status) {
    case "accepted": return "bg-success/10 text-success border-success/20";
    case "sent": return "bg-accent/10 text-accent border-accent/20";
    case "follow_up": return "bg-warning/10 text-warning border-warning/20";
    case "declined": return "bg-destructive/10 text-destructive border-destructive/20";
    case "draft": return "bg-muted/10 text-muted-foreground border-muted/20";
    default: return "bg-muted/10 text-muted-foreground border-muted/20";
  }
};

export default function Quotes() {
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const [showCreateForm, setShowCreateForm] = useState(false);

  const filteredQuotes = quotes.filter(quote => {
    const matchesSearch = quote.clientName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         quote.id.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === "all" || quote.status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  return (
    <DashboardLayout>
      <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col lg:flex-row gap-4 justify-between items-start lg:items-center">
        <div>
          <h1 className="text-3xl font-bold text-foreground mb-2">
            Quotes & Proposals
          </h1>
          <p className="text-muted-foreground">
            Create, manage and track client quotes and proposals
          </p>
        </div>
        <Button className="gap-2" onClick={() => setShowCreateForm(true)}>
          <Plus className="h-4 w-4" />
          Create Quote
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card className="shadow-card border-border">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Quotes</p>
                <p className="text-2xl font-bold">45</p>
              </div>
              <FileText className="h-8 w-8 text-primary" />
            </div>
            <p className="text-xs text-muted-foreground mt-2">+8 this month</p>
          </CardContent>
        </Card>

        <Card className="shadow-card border-border">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Accepted</p>
                <p className="text-2xl font-bold">28</p>
              </div>
              <CheckCircle className="h-8 w-8 text-success" />
            </div>
            <p className="text-xs text-muted-foreground mt-2">62% conversion rate</p>
          </CardContent>
        </Card>

        <Card className="shadow-card border-border">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Pending</p>
                <p className="text-2xl font-bold">12</p>
              </div>
              <Clock className="h-8 w-8 text-warning" />
            </div>
            <p className="text-xs text-muted-foreground mt-2">Awaiting response</p>
          </CardContent>
        </Card>

        <Card className="shadow-card border-border">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Quote Value</p>
                <p className="text-2xl font-bold">₹12.5L</p>
              </div>
              <DollarSign className="h-8 w-8 text-accent" />
            </div>
            <p className="text-xs text-muted-foreground mt-2">Total quoted amount</p>
          </CardContent>
        </Card>
      </div>

      {/* Filters and Search */}
      <Card className="shadow-card border-border">
        <CardContent className="p-6">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search quotes by client name or quote ID..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-full md:w-40">
                <SelectValue placeholder="Filter status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="draft">Draft</SelectItem>
                <SelectItem value="sent">Sent</SelectItem>
                <SelectItem value="accepted">Accepted</SelectItem>
                <SelectItem value="declined">Declined</SelectItem>
                <SelectItem value="follow_up">Follow Up</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Quotes Table */}
      <Card className="shadow-card border-border">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            All Quotes
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Quote ID</TableHead>
                  <TableHead>Client</TableHead>
                  <TableHead>Event Type</TableHead>
                  <TableHead>Amount</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Valid Until</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredQuotes.map((quote) => (
                  <TableRow key={quote.id}>
                    <TableCell className="font-medium">{quote.id}</TableCell>
                    <TableCell>
                      <div>
                        <p className="font-medium">{quote.clientName}</p>
                        <p className="text-sm text-muted-foreground">{quote.contactEmail}</p>
                      </div>
                    </TableCell>
                    <TableCell>{quote.eventType}</TableCell>
                    <TableCell className="font-medium">{quote.quotedAmount}</TableCell>
                    <TableCell>
                      <Badge variant="outline" className={getStatusColor(quote.status)}>
                        {quote.status.replace('_', ' ')}
                      </Badge>
                    </TableCell>
                    <TableCell>{quote.validUntil}</TableCell>
                    <TableCell>
                      <div className="flex gap-2">
                        <Dialog>
                          <DialogTrigger asChild>
                            <Button variant="outline" size="sm">
                              <Eye className="h-4 w-4" />
                            </Button>
                          </DialogTrigger>
                          <DialogContent className="max-w-2xl">
                            <DialogHeader>
                              <DialogTitle>Quote Details - {quote.id}</DialogTitle>
                              <DialogDescription>
                                Complete quote information and management options
                              </DialogDescription>
                            </DialogHeader>
                            <div className="space-y-4">
                              <div className="grid grid-cols-2 gap-4">
                                <div>
                                  <label className="text-sm font-medium">Client Name</label>
                                  <p className="text-sm text-muted-foreground">{quote.clientName}</p>
                                </div>
                                <div>
                                  <label className="text-sm font-medium">Event Date</label>
                                  <p className="text-sm text-muted-foreground">{quote.eventDate}</p>
                                </div>
                              </div>
                              <div>
                                <label className="text-sm font-medium">Services Included</label>
                                <ul className="mt-2 space-y-1">
                                  {quote.services.map((service, index) => (
                                    <li key={index} className="text-sm text-muted-foreground">• {service}</li>
                                  ))}
                                </ul>
                              </div>
                              <div className="flex gap-2 mt-4">
                                <Button variant="outline">Edit Quote</Button>
                                <Button variant="outline">Send Email</Button>
                                <Button>Convert to Order</Button>
                              </div>
                            </div>
                          </DialogContent>
                        </Dialog>
                        <Button variant="outline" size="sm">
                          <Send className="h-4 w-4" />
                        </Button>
                        <Button variant="outline" size="sm">
                          <Download className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>

      {/* Create Quote Dialog */}
      <Dialog open={showCreateForm} onOpenChange={setShowCreateForm}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Create New Quote</DialogTitle>
            <DialogDescription>
              Generate a professional quote for your client
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="clientName">Client Name</Label>
                <Input id="clientName" placeholder="Enter client name" />
              </div>
              <div>
                <Label htmlFor="clientEmail">Email</Label>
                <Input id="clientEmail" type="email" placeholder="<EMAIL>" />
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="eventType">Event Type</Label>
                <Select>
                  <SelectTrigger>
                    <SelectValue placeholder="Select event type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="wedding">Wedding</SelectItem>
                    <SelectItem value="corporate">Corporate Event</SelectItem>
                    <SelectItem value="baby">Baby Shoot</SelectItem>
                    <SelectItem value="fashion">Fashion Shoot</SelectItem>
                    <SelectItem value="product">Product Photography</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label htmlFor="eventDate">Event Date</Label>
                <Input id="eventDate" type="date" />
              </div>
            </div>
            <div>
              <Label htmlFor="services">Services</Label>
              <Textarea id="services" placeholder="List the services included in this quote..." />
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="amount">Quote Amount</Label>
                <Input id="amount" placeholder="₹0" />
              </div>
              <div>
                <Label htmlFor="validUntil">Valid Until</Label>
                <Input id="validUntil" type="date" />
              </div>
            </div>
            <div className="flex gap-2 mt-6">
              <Button variant="outline" onClick={() => setShowCreateForm(false)}>
                Cancel
              </Button>
              <Button variant="outline">Save as Draft</Button>
              <Button onClick={() => setShowCreateForm(false)}>
                Create & Send Quote
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
      </div>
    </DashboardLayout>
  );
}