import { Toaster } from "@/components/ui/toaster";
import { Toaster as Son<PERSON> } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import Index from "./pages/Index";
import Events from "./pages/Events";
import Clients from "./pages/Clients";
import Gallery from "./pages/Gallery";
import Website from "./pages/Website";
import MarketingWebsite from "./pages/MarketingWebsite";
import Login from "./pages/Login";
import NotFound from "./pages/NotFound";

const queryClient = new QueryClient();

const App = () => (
  <QueryClientProvider client={queryClient}>
    <TooltipProvider>
      <Toaster />
      <Sonner />
      <BrowserRouter>
        <Routes>
          <Route path="/" element={<MarketingWebsite />} />
          <Route path="/admin" element={<Index />} />
          <Route path="/admin/events" element={<Events />} />
          <Route path="/admin/clients" element={<Clients />} />
          <Route path="/admin/gallery" element={<Gallery />} />
          <Route path="/admin/website" element={<Website />} />
          <Route path="/login" element={<Login />} />
          {/* ADD ALL CUSTOM ROUTES ABOVE THE CATCH-ALL "*" ROUTE */}
          <Route path="*" element={<NotFound />} />
        </Routes>
      </BrowserRouter>
    </TooltipProvider>
  </QueryClientProvider>
);

export default App;
