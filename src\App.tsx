import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import Index from "./pages/Index";
import Events from "./pages/Events";
import Clients from "./pages/Clients";
import Gallery from "./pages/Gallery";
import Orders from "./pages/Orders";
import Quotes from "./pages/Quotes";
import Finance from "./pages/Finance";
import WebsiteAdmin from "./pages/WebsiteAdmin";
import Freelancers from "./pages/Freelancers";
import Vendors from "./pages/Vendors";
import Team from "./pages/Team";
import Notifications from "./pages/Notifications";
import WhatsApp from "./pages/WhatsApp";
import Settings from "./pages/Settings";
import Website from "./pages/Website";
import MarketingWebsite from "./pages/MarketingWebsite";
import Login from "./pages/Login";
import NotFound from "./pages/NotFound";

const queryClient = new QueryClient();

const App = () => (
  <QueryClientProvider client={queryClient}>
    <TooltipProvider>
      <Toaster />
      <Sonner />
      <BrowserRouter>
        <Routes>
          <Route path="/" element={<MarketingWebsite />} />
          <Route path="/admin" element={<Index />} />
          <Route path="/admin/events" element={<Events />} />
          <Route path="/admin/clients" element={<Clients />} />
          <Route path="/admin/gallery" element={<Gallery />} />
          <Route path="/admin/orders" element={<Orders />} />
          <Route path="/admin/quotes" element={<Quotes />} />
          <Route path="/admin/finance" element={<Finance />} />
          <Route path="/admin/website" element={<WebsiteAdmin />} />
          <Route path="/admin/freelancers" element={<Freelancers />} />
          <Route path="/admin/vendors" element={<Vendors />} />
          <Route path="/admin/team" element={<Team />} />
          <Route path="/admin/notifications" element={<Notifications />} />
          <Route path="/admin/whatsapp" element={<WhatsApp />} />
          <Route path="/admin/settings" element={<Settings />} />
          <Route path="/login" element={<Login />} />
          {/* ADD ALL CUSTOM ROUTES ABOVE THE CATCH-ALL "*" ROUTE */}
          <Route path="*" element={<NotFound />} />
        </Routes>
      </BrowserRouter>
    </TooltipProvider>
  </QueryClientProvider>
);

export default App;
