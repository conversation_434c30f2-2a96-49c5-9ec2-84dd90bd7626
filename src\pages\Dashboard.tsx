import { StatsCard } from "@/components/dashboard/StatsCard";
import { QuickActions } from "@/components/dashboard/QuickActions";
import { RecentActivity } from "@/components/dashboard/RecentActivity";
import { UpcomingEvents } from "@/components/dashboard/UpcomingEvents";
import { 
  Calendar,
  Upload,
  Download,
  Users,
  CreditCard,
  TrendingUp,
  Camera,
  Image,
  Clock,
  AlertTriangle
} from "lucide-react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import studioHero from "@/assets/studio-hero.jpg";
import eventCollage from "@/assets/event-collage.jpg";

export default function Dashboard() {
  return (
    <div className="space-y-6">
      {/* Header Section */}
      <div className="flex flex-col lg:flex-row gap-6">
        <div className="flex-1">
          <div className="mb-6">
            <h1 className="text-3xl font-bold text-foreground mb-2">
              Om Digital Studio Dashboard
            </h1>
            <p className="text-muted-foreground">
              Welcome back! Here's what's happening with your photography business today.
            </p>
          </div>

          {/* Stats Cards Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
            <StatsCard
              title="Active Events"
              value="12"
              change={{ value: "+2 this week", type: "increase" }}
              icon={Calendar}
              description="from last month"
            />
            <StatsCard
              title="Photos Uploaded"
              value="2,847"
              change={{ value: "+15%", type: "increase" }}
              icon={Upload}
              description="this month"
            />
            <StatsCard
              title="Downloads"
              value="1,234"
              change={{ value: "+8%", type: "increase" }}
              icon={Download}
              description="client downloads"
            />
            <StatsCard
              title="Revenue"
              value="₹2,45,000"
              change={{ value: "+22%", type: "increase" }}
              icon={CreditCard}
              description="this month"
            />
          </div>
        </div>

        {/* Hero Image */}
        <div className="lg:w-80">
          <Card className="overflow-hidden shadow-card border-border">
            <div className="relative h-48 lg:h-full">
              <img
                src={studioHero}
                alt="Photography Studio"
                className="w-full h-full object-cover"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent" />
              <div className="absolute bottom-4 left-4 right-4">
                <h3 className="text-white font-semibold mb-1">Professional Studio</h3>
                <p className="text-white/80 text-sm">Creating beautiful memories</p>
              </div>
            </div>
          </Card>
        </div>
      </div>

      {/* Quick Actions */}
      <QuickActions />

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Left Column - Events and Storage */}
        <div className="lg:col-span-2 space-y-6">
          <UpcomingEvents />
          
          {/* Storage & Performance */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card className="shadow-card border-border">
              <CardHeader>
                <CardTitle className="text-lg font-semibold flex items-center gap-2">
                  <Upload className="h-5 w-5 text-primary" />
                  Storage Usage
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <div className="flex justify-between mb-2">
                    <span className="text-sm text-muted-foreground">Photos</span>
                    <span className="text-sm font-medium">450 GB / 1 TB</span>
                  </div>
                  <Progress value={45} className="h-2" />
                </div>
                <div>
                  <div className="flex justify-between mb-2">
                    <span className="text-sm text-muted-foreground">Videos</span>
                    <span className="text-sm font-medium">120 GB / 500 GB</span>
                  </div>
                  <Progress value={24} className="h-2" />
                </div>
                <div>
                  <div className="flex justify-between mb-2">
                    <span className="text-sm text-muted-foreground">Backups</span>
                    <span className="text-sm font-medium">80 GB / 200 GB</span>
                  </div>
                  <Progress value={40} className="h-2" />
                </div>
              </CardContent>
            </Card>

            <Card className="shadow-card border-border">
              <CardHeader>
                <CardTitle className="text-lg font-semibold flex items-center gap-2">
                  <TrendingUp className="h-5 w-5 text-primary" />
                  Performance
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">Client Satisfaction</span>
                  <Badge variant="outline" className="bg-success/10 text-success border-success/20">
                    98%
                  </Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">Delivery Time</span>
                  <Badge variant="outline" className="bg-success/10 text-success border-success/20">
                    2.3 days avg
                  </Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">Upload Speed</span>
                  <Badge variant="outline" className="bg-accent/10 text-accent border-accent/20">
                    45 MB/s
                  </Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">Active Sessions</span>
                  <Badge variant="outline" className="bg-primary/10 text-primary border-primary/20">
                    23 online
                  </Badge>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Pending Tasks */}
          <Card className="shadow-card border-border">
            <CardHeader>
              <CardTitle className="text-lg font-semibold flex items-center gap-2">
                <AlertTriangle className="h-5 w-5 text-warning" />
                Pending Actions
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="flex items-center justify-between p-3 bg-warning/5 border border-warning/20 rounded-lg">
                  <div className="flex items-center gap-3">
                    <Image className="h-5 w-5 text-warning" />
                    <div>
                      <p className="font-medium text-sm">Guest Photos Approval</p>
                      <p className="text-xs text-muted-foreground">12 photos waiting for review</p>
                    </div>
                  </div>
                  <Badge variant="outline" className="bg-warning/10 text-warning border-warning/20">
                    Review
                  </Badge>
                </div>
                <div className="flex items-center justify-between p-3 bg-destructive/5 border border-destructive/20 rounded-lg">
                  <div className="flex items-center gap-3">
                    <CreditCard className="h-5 w-5 text-destructive" />
                    <div>
                      <p className="font-medium text-sm">Payment Overdue</p>
                      <p className="text-xs text-muted-foreground">₹15,000 from TechCorp event</p>
                    </div>
                  </div>
                  <Badge variant="outline" className="bg-destructive/10 text-destructive border-destructive/20">
                    Follow-up
                  </Badge>
                </div>
                <div className="flex items-center justify-between p-3 bg-accent/5 border border-accent/20 rounded-lg">
                  <div className="flex items-center gap-3">
                    <Clock className="h-5 w-5 text-accent" />
                    <div>
                      <p className="font-medium text-sm">Delivery Due</p>
                      <p className="text-xs text-muted-foreground">Sarah & John wedding album by Dec 20</p>
                    </div>
                  </div>
                  <Badge variant="outline" className="bg-accent/10 text-accent border-accent/20">
                    Urgent
                  </Badge>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Right Column - Activity */}
        <div className="space-y-6">
          <RecentActivity />
          
          {/* Portfolio Preview */}
          <Card className="shadow-card border-border">
            <CardHeader>
              <CardTitle className="text-lg font-semibold flex items-center gap-2">
                <Camera className="h-5 w-5 text-primary" />
                Featured Work
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="relative rounded-lg overflow-hidden">
                <img
                  src={eventCollage}
                  alt="Featured Photography Work"
                  className="w-full h-48 object-cover"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent" />
                <div className="absolute bottom-4 left-4 right-4">
                  <h4 className="text-white font-semibold mb-1">Latest Portfolio</h4>
                  <p className="text-white/80 text-sm">Wedding & Event Photography</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}