import { useState } from "react";
import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, Card<PERSON>itle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Textarea } from "@/components/ui/textarea";
import { 
  Building, 
  Search, 
  Plus, 
  Eye,
  Edit,
  Star,
  MapPin,
  Phone,
  Mail,
  Package,
  Printer,
  Truck,
  Camera,
  Palette,
  Music,
  CheckCircle,
  Clock,
  XCircle
} from "lucide-react";
import { DashboardLayout } from "@/components/layout/DashboardLayout";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";

const vendors = [
  {
    id: 1,
    name: "PrintCraft Studio",
    type: "Printing Services",
    contact: "<PERSON><PERSON>",
    email: "<EMAIL>",
    phone: "9876543210",
    address: "MG Road, Surat, Gujarat",
    services: ["Photo Prints", "Albums", "Canvas Prints", "Framing"],
    rating: 4.8,
    totalOrders: 156,
    status: "active",
    paymentTerms: "Net 15",
    joinDate: "2023-01-15"
  },
  {
    id: 2,
    name: "Quick Delivery Co.",
    type: "Courier Services", 
    contact: "Amit Shah",
    email: "<EMAIL>",
    phone: "9876543211",
    address: "Industrial Area, Bardoli, Gujarat",
    services: ["Same Day Delivery", "Express Shipping", "Bulk Orders"],
    rating: 4.6,
    totalOrders: 89,
    status: "active",
    paymentTerms: "Net 7",
    joinDate: "2023-03-20"
  },
  {
    id: 3,
    name: "Tech Equipment Rental",
    type: "Equipment Rental",
    contact: "Priya Patel", 
    email: "<EMAIL>",
    phone: "9876543212",
    address: "Tech Park, Ahmedabad, Gujarat",
    services: ["Camera Rental", "Lighting Equipment", "Video Gear"],
    rating: 4.9,
    totalOrders: 67,
    status: "active",
    paymentTerms: "Advance",
    joinDate: "2023-05-10"
  },
  {
    id: 4,
    name: "Décor & Events",
    type: "Event Decoration",
    contact: "Sneha Modi",
    email: "<EMAIL>", 
    phone: "9876543213",
    address: "Wedding Street, Mumbai, Maharashtra",
    services: ["Wedding Décor", "Stage Setup", "Floral Arrangements"],
    rating: 4.7,
    totalOrders: 45,
    status: "inactive",
    paymentTerms: "50% Advance",
    joinDate: "2023-07-05"
  },
  {
    id: 5,
    name: "Digital Lab Pro",
    type: "Photo Lab",
    contact: "Kiran Desai",
    email: "<EMAIL>",
    phone: "9876543214", 
    address: "Digital Hub, Pune, Maharashtra",
    services: ["Photo Development", "Color Correction", "Large Format Prints"],
    rating: 4.5,
    totalOrders: 234,
    status: "active",
    paymentTerms: "Net 30",
    joinDate: "2022-12-01"
  }
];

const getStatusColor = (status: string) => {
  switch (status) {
    case "active": return "bg-success/10 text-success border-success/20";
    case "inactive": return "bg-destructive/10 text-destructive border-destructive/20";
    case "pending": return "bg-warning/10 text-warning border-warning/20";
    default: return "bg-muted/10 text-muted-foreground border-muted/20";
  }
};

const getTypeIcon = (type: string) => {
  switch (type) {
    case "Printing Services": return Printer;
    case "Courier Services": return Truck;
    case "Equipment Rental": return Camera;
    case "Event Decoration": return Palette;
    case "Photo Lab": return Package;
    default: return Building;
  }
};

export default function Vendors() {
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const [typeFilter, setTypeFilter] = useState("all");
  const [showAddForm, setShowAddForm] = useState(false);

  const filteredVendors = vendors.filter(vendor => {
    const matchesSearch = vendor.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         vendor.type.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === "all" || vendor.status === statusFilter;
    const matchesType = typeFilter === "all" || vendor.type === typeFilter;
    return matchesSearch && matchesStatus && matchesType;
  });

  const activeCount = vendors.filter(v => v.status === "active").length;
  const totalOrders = vendors.reduce((sum, v) => sum + v.totalOrders, 0);
  const avgRating = vendors.reduce((sum, v) => sum + v.rating, 0) / vendors.length;

  return (
    <DashboardLayout>
      <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col lg:flex-row gap-4 justify-between items-start lg:items-center">
        <div>
          <h1 className="text-3xl font-bold text-foreground mb-2">
            Vendors Management
          </h1>
          <p className="text-muted-foreground">
            Manage your vendor network and supplier relationships
          </p>
        </div>
        <Button className="gap-2" onClick={() => setShowAddForm(true)}>
          <Plus className="h-4 w-4" />
          Add Vendor
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card className="shadow-card border-border">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Vendors</p>
                <p className="text-2xl font-bold">{vendors.length}</p>
              </div>
              <Building className="h-8 w-8 text-primary" />
            </div>
            <p className="text-xs text-muted-foreground mt-2">Registered suppliers</p>
          </CardContent>
        </Card>

        <Card className="shadow-card border-border">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Active Vendors</p>
                <p className="text-2xl font-bold">{activeCount}</p>
              </div>
              <CheckCircle className="h-8 w-8 text-success" />
            </div>
            <p className="text-xs text-muted-foreground mt-2">Currently active</p>
          </CardContent>
        </Card>

        <Card className="shadow-card border-border">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Orders</p>
                <p className="text-2xl font-bold">{totalOrders}</p>
              </div>
              <Package className="h-8 w-8 text-accent" />
            </div>
            <p className="text-xs text-muted-foreground mt-2">All time orders</p>
          </CardContent>
        </Card>

        <Card className="shadow-card border-border">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Average Rating</p>
                <p className="text-2xl font-bold">{avgRating.toFixed(1)}</p>
              </div>
              <Star className="h-8 w-8 text-warning" />
            </div>
            <p className="text-xs text-muted-foreground mt-2">Vendor quality score</p>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card className="shadow-card border-border">
        <CardContent className="p-6">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search vendors by name or type..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <Select value={typeFilter} onValueChange={setTypeFilter}>
              <SelectTrigger className="w-full md:w-48">
                <SelectValue placeholder="Filter by type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Types</SelectItem>
                <SelectItem value="Printing Services">Printing Services</SelectItem>
                <SelectItem value="Courier Services">Courier Services</SelectItem>
                <SelectItem value="Equipment Rental">Equipment Rental</SelectItem>
                <SelectItem value="Event Decoration">Event Decoration</SelectItem>
                <SelectItem value="Photo Lab">Photo Lab</SelectItem>
              </SelectContent>
            </Select>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-full md:w-40">
                <SelectValue placeholder="Filter status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="active">Active</SelectItem>
                <SelectItem value="inactive">Inactive</SelectItem>
                <SelectItem value="pending">Pending</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Vendors Table */}
      <Card className="shadow-card border-border">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Building className="h-5 w-5" />
            Vendor Directory
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Vendor</TableHead>
                  <TableHead>Type & Services</TableHead>
                  <TableHead>Contact</TableHead>
                  <TableHead>Rating</TableHead>
                  <TableHead>Orders</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredVendors.map((vendor) => {
                  const TypeIcon = getTypeIcon(vendor.type);
                  return (
                    <TableRow key={vendor.id}>
                      <TableCell>
                        <div className="flex items-center gap-3">
                          <div className="p-2 bg-primary/10 rounded-full">
                            <TypeIcon className="h-5 w-5 text-primary" />
                          </div>
                          <div>
                            <p className="font-medium">{vendor.name}</p>
                            <div className="flex items-center gap-1 text-sm text-muted-foreground">
                              <MapPin className="h-3 w-3" />
                              {vendor.address.split(',')[0]}
                            </div>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div>
                          <p className="font-medium text-sm">{vendor.type}</p>
                          <div className="flex flex-wrap gap-1 mt-1">
                            {vendor.services.slice(0, 2).map((service, index) => (
                              <Badge key={index} variant="outline" className="text-xs">
                                {service}
                              </Badge>
                            ))}
                            {vendor.services.length > 2 && (
                              <Badge variant="outline" className="text-xs">
                                +{vendor.services.length - 2}
                              </Badge>
                            )}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div>
                          <p className="font-medium text-sm">{vendor.contact}</p>
                          <div className="flex items-center gap-1 text-xs text-muted-foreground">
                            <Phone className="h-3 w-3" />
                            {vendor.phone}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-1">
                          <Star className="h-4 w-4 text-warning fill-current" />
                          <span className="font-medium">{vendor.rating}</span>
                        </div>
                      </TableCell>
                      <TableCell className="font-medium">{vendor.totalOrders}</TableCell>
                      <TableCell>
                        <Badge variant="outline" className={getStatusColor(vendor.status)}>
                          {vendor.status}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div className="flex gap-2">
                          <Dialog>
                            <DialogTrigger asChild>
                              <Button variant="outline" size="sm">
                                <Eye className="h-4 w-4" />
                              </Button>
                            </DialogTrigger>
                            <DialogContent className="max-w-2xl">
                              <DialogHeader>
                                <DialogTitle>Vendor Details - {vendor.name}</DialogTitle>
                                <DialogDescription>
                                  Complete vendor information and order history
                                </DialogDescription>
                              </DialogHeader>
                              <div className="space-y-4">
                                <div className="grid grid-cols-2 gap-4">
                                  <div>
                                    <label className="text-sm font-medium">Contact Person</label>
                                    <p className="text-sm text-muted-foreground">{vendor.contact}</p>
                                  </div>
                                  <div>
                                    <label className="text-sm font-medium">Payment Terms</label>
                                    <p className="text-sm text-muted-foreground">{vendor.paymentTerms}</p>
                                  </div>
                                </div>
                                <div>
                                  <label className="text-sm font-medium">Services Offered</label>
                                  <div className="flex flex-wrap gap-2 mt-2">
                                    {vendor.services.map((service, index) => (
                                      <Badge key={index} variant="outline">{service}</Badge>
                                    ))}
                                  </div>
                                </div>
                                <div>
                                  <label className="text-sm font-medium">Full Address</label>
                                  <p className="text-sm text-muted-foreground">{vendor.address}</p>
                                </div>
                                <div className="flex gap-2 mt-4">
                                  <Button variant="outline">Edit Details</Button>
                                  <Button variant="outline">Create Order</Button>
                                  <Button>Send Message</Button>
                                </div>
                              </div>
                            </DialogContent>
                          </Dialog>
                          <Button variant="outline" size="sm">
                            <Edit className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  );
                })}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>

      {/* Add Vendor Dialog */}
      <Dialog open={showAddForm} onOpenChange={setShowAddForm}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Add New Vendor</DialogTitle>
            <DialogDescription>
              Add a new vendor to your supplier network
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="vendorName">Vendor Name</Label>
                <Input id="vendorName" placeholder="Enter vendor name" />
              </div>
              <div>
                <Label htmlFor="vendorType">Vendor Type</Label>
                <Select>
                  <SelectTrigger>
                    <SelectValue placeholder="Select vendor type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="printing">Printing Services</SelectItem>
                    <SelectItem value="courier">Courier Services</SelectItem>
                    <SelectItem value="equipment">Equipment Rental</SelectItem>
                    <SelectItem value="decoration">Event Decoration</SelectItem>
                    <SelectItem value="lab">Photo Lab</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="contactPerson">Contact Person</Label>
                <Input id="contactPerson" placeholder="Contact person name" />
              </div>
              <div>
                <Label htmlFor="vendorPhone">Phone Number</Label>
                <Input id="vendorPhone" placeholder="Enter phone number" />
              </div>
            </div>
            <div>
              <Label htmlFor="vendorEmail">Email</Label>
              <Input id="vendorEmail" type="email" placeholder="<EMAIL>" />
            </div>
            <div>
              <Label htmlFor="vendorAddress">Address</Label>
              <Textarea id="vendorAddress" placeholder="Enter complete address..." />
            </div>
            <div>
              <Label htmlFor="vendorServices">Services Offered</Label>
              <Textarea id="vendorServices" placeholder="List services separated by commas..." />
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="paymentTerms">Payment Terms</Label>
                <Select>
                  <SelectTrigger>
                    <SelectValue placeholder="Select payment terms" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="advance">100% Advance</SelectItem>
                    <SelectItem value="net7">Net 7 Days</SelectItem>
                    <SelectItem value="net15">Net 15 Days</SelectItem>
                    <SelectItem value="net30">Net 30 Days</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label htmlFor="initialRating">Initial Rating</Label>
                <Select>
                  <SelectTrigger>
                    <SelectValue placeholder="Rate vendor" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="5">5 Stars</SelectItem>
                    <SelectItem value="4">4 Stars</SelectItem>
                    <SelectItem value="3">3 Stars</SelectItem>
                    <SelectItem value="2">2 Stars</SelectItem>
                    <SelectItem value="1">1 Star</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            <div className="flex gap-2 mt-6">
              <Button variant="outline" onClick={() => setShowAddForm(false)}>
                Cancel
              </Button>
              <Button onClick={() => setShowAddForm(false)}>
                Add Vendor
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
      </div>
    </DashboardLayout>
  );
}