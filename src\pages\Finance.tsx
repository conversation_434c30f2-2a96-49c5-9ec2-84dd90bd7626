import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { 
  DollarSign, 
  Search, 
  Plus, 
  Download,
  TrendingUp,
  TrendingDown,
  CreditCard,
  Wallet,
  Receipt,
  Calculator,
  Calendar,
  ArrowUpRight,
  ArrowDownRight,
  Clock
} from "lucide-react";
import { DashboardLayout } from "@/components/layout/DashboardLayout";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Progress } from "@/components/ui/progress";

const transactions = [
  {
    id: "TXN-001",
    date: "2024-01-28",
    type: "income",
    description: "Wedding Photography - Priya & Raj",
    category: "Photography Services",
    amount: "₹85,000",
    status: "completed",
    paymentMethod: "Bank Transfer"
  },
  {
    id: "TXN-002",
    date: "2024-01-27",
    type: "expense", 
    description: "Camera Equipment Purchase",
    category: "Equipment",
    amount: "₹45,000",
    status: "completed",
    paymentMethod: "Credit Card"
  },
  {
    id: "TXN-003",
    date: "2024-01-26",
    type: "income",
    description: "Corporate Event - TechCorp",
    category: "Event Photography",
    amount: "₹35,000",
    status: "pending",
    paymentMethod: "UPI"
  },
  {
    id: "TXN-004",
    date: "2024-01-25",
    type: "expense",
    description: "Studio Rent - January",
    category: "Rent",
    amount: "₹15,000",
    status: "completed",
    paymentMethod: "Bank Transfer"
  },
  {
    id: "TXN-005",
    date: "2024-01-24",
    type: "income",
    description: "Product Photography - EcoFriendly",
    category: "Product Photography",
    amount: "₹28,000",
    status: "completed",
    paymentMethod: "Bank Transfer"
  }
];

const monthlyData = [
  { month: "Jan", income: 285000, expenses: 120000 },
  { month: "Feb", income: 320000, expenses: 135000 },
  { month: "Mar", income: 298000, expenses: 128000 },
  { month: "Apr", income: 410000, expenses: 155000 },
  { month: "May", income: 385000, expenses: 142000 },
  { month: "Jun", income: 445000, expenses: 168000 }
];

export default function Finance() {
  const [searchTerm, setSearchTerm] = useState("");
  const [typeFilter, setTypeFilter] = useState("all");
  const [dateRange, setDateRange] = useState("this_month");

  const filteredTransactions = transactions.filter(transaction => {
    const matchesSearch = transaction.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         transaction.id.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesType = typeFilter === "all" || transaction.type === typeFilter;
    return matchesSearch && matchesType;
  });

  const totalIncome = transactions
    .filter(t => t.type === "income" && t.status === "completed")
    .reduce((sum, t) => sum + parseInt(t.amount.replace(/[₹,]/g, "")), 0);

  const totalExpenses = transactions
    .filter(t => t.type === "expense")
    .reduce((sum, t) => sum + parseInt(t.amount.replace(/[₹,]/g, "")), 0);

  const netProfit = totalIncome - totalExpenses;

  return (
    <DashboardLayout>
      <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col lg:flex-row gap-4 justify-between items-start lg:items-center">
        <div>
          <h1 className="text-3xl font-bold text-foreground mb-2">
            Finance & Reports
          </h1>
          <p className="text-muted-foreground">
            Track income, expenses, and generate financial reports
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" className="gap-2">
            <Download className="h-4 w-4" />
            Export Report
          </Button>
          <Button className="gap-2">
            <Plus className="h-4 w-4" />
            Add Transaction
          </Button>
        </div>
      </div>

      {/* Financial Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card className="shadow-card border-border">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Income</p>
                <p className="text-2xl font-bold text-success">₹{totalIncome.toLocaleString()}</p>
              </div>
              <div className="p-3 bg-success/10 rounded-full">
                <TrendingUp className="h-6 w-6 text-success" />
              </div>
            </div>
            <div className="flex items-center gap-1 mt-2">
              <ArrowUpRight className="h-4 w-4 text-success" />
              <p className="text-xs text-success">+12% from last month</p>
            </div>
          </CardContent>
        </Card>

        <Card className="shadow-card border-border">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Expenses</p>
                <p className="text-2xl font-bold text-destructive">₹{totalExpenses.toLocaleString()}</p>
              </div>
              <div className="p-3 bg-destructive/10 rounded-full">
                <TrendingDown className="h-6 w-6 text-destructive" />
              </div>
            </div>
            <div className="flex items-center gap-1 mt-2">
              <ArrowDownRight className="h-4 w-4 text-destructive" />
              <p className="text-xs text-destructive">+5% from last month</p>
            </div>
          </CardContent>
        </Card>

        <Card className="shadow-card border-border">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Net Profit</p>
                <p className="text-2xl font-bold text-primary">₹{netProfit.toLocaleString()}</p>
              </div>
              <div className="p-3 bg-primary/10 rounded-full">
                <DollarSign className="h-6 w-6 text-primary" />
              </div>
            </div>
            <div className="flex items-center gap-1 mt-2">
              <ArrowUpRight className="h-4 w-4 text-success" />
              <p className="text-xs text-success">+18% from last month</p>
            </div>
          </CardContent>
        </Card>

        <Card className="shadow-card border-border">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Pending Payments</p>
                <p className="text-2xl font-bold text-warning">₹45,000</p>
              </div>
              <div className="p-3 bg-warning/10 rounded-full">
                <Clock className="h-6 w-6 text-warning" />
              </div>
            </div>
            <p className="text-xs text-muted-foreground mt-2">3 overdue invoices</p>
          </CardContent>
        </Card>
      </div>

      {/* Monthly Performance Chart */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <Card className="lg:col-span-2 shadow-card border-border">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="h-5 w-5" />
              Monthly Performance
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {monthlyData.map((data) => (
                <div key={data.month} className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span className="font-medium">{data.month}</span>
                    <span className="text-muted-foreground">
                      Income: ₹{data.income.toLocaleString()} | Expenses: ₹{data.expenses.toLocaleString()}
                    </span>
                  </div>
                  <div className="space-y-1">
                    <Progress value={(data.income / 500000) * 100} className="h-2 bg-success/20" />
                    <Progress value={(data.expenses / 500000) * 100} className="h-2 bg-destructive/20" />
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        <Card className="shadow-card border-border">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Calculator className="h-5 w-5" />
              Tax Summary
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex justify-between">
              <span className="text-sm text-muted-foreground">Gross Income</span>
              <span className="font-medium">₹3,48,000</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-muted-foreground">Business Expenses</span>
              <span className="font-medium">₹1,20,000</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-muted-foreground">Taxable Income</span>
              <span className="font-medium">₹2,28,000</span>
            </div>
            <div className="border-t pt-2">
              <div className="flex justify-between">
                <span className="text-sm font-medium">Estimated Tax (30%)</span>
                <span className="font-bold text-primary">₹68,400</span>
              </div>
            </div>
            <Button className="w-full gap-2">
              <Receipt className="h-4 w-4" />
              Generate Tax Report
            </Button>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card className="shadow-card border-border">
        <CardContent className="p-6">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search transactions..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <Select value={typeFilter} onValueChange={setTypeFilter}>
              <SelectTrigger className="w-full md:w-40">
                <SelectValue placeholder="Transaction type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Types</SelectItem>
                <SelectItem value="income">Income</SelectItem>
                <SelectItem value="expense">Expenses</SelectItem>
              </SelectContent>
            </Select>
            <Select value={dateRange} onValueChange={setDateRange}>
              <SelectTrigger className="w-full md:w-40">
                <SelectValue placeholder="Date range" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="this_month">This Month</SelectItem>
                <SelectItem value="last_month">Last Month</SelectItem>
                <SelectItem value="this_quarter">This Quarter</SelectItem>
                <SelectItem value="this_year">This Year</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Transactions Table */}
      <Card className="shadow-card border-border">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Wallet className="h-5 w-5" />
            Recent Transactions
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Date</TableHead>
                  <TableHead>Description</TableHead>
                  <TableHead>Category</TableHead>
                  <TableHead>Type</TableHead>
                  <TableHead>Amount</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Payment Method</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredTransactions.map((transaction) => (
                  <TableRow key={transaction.id}>
                    <TableCell>{transaction.date}</TableCell>
                    <TableCell className="font-medium">{transaction.description}</TableCell>
                    <TableCell>{transaction.category}</TableCell>
                    <TableCell>
                      <Badge 
                        variant="outline" 
                        className={transaction.type === "income" 
                          ? "bg-success/10 text-success border-success/20"
                          : "bg-destructive/10 text-destructive border-destructive/20"
                        }
                      >
                        {transaction.type === "income" ? "Income" : "Expense"}
                      </Badge>
                    </TableCell>
                    <TableCell className={`font-medium ${
                      transaction.type === "income" ? "text-success" : "text-destructive"
                    }`}>
                      {transaction.type === "income" ? "+" : "-"}{transaction.amount}
                    </TableCell>
                    <TableCell>
                      <Badge 
                        variant="outline"
                        className={transaction.status === "completed"
                          ? "bg-success/10 text-success border-success/20"
                          : "bg-warning/10 text-warning border-warning/20"
                        }
                      >
                        {transaction.status}
                      </Badge>
                    </TableCell>
                    <TableCell>{transaction.paymentMethod}</TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>
      </div>
    </DashboardLayout>
  );
}