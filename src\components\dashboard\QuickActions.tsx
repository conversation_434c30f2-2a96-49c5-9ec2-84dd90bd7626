import { Button } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { 
  Plus, 
  QrCode, 
  Send, 
  Upload, 
  Calendar,
  Users,
  FileText,
  Image
} from "lucide-react";

const actions = [
  {
    title: "Create Event",
    description: "Setup new photo shoot",
    icon: Plus,
    variant: "gradient" as const,
    onClick: () => console.log("Create Event")
  },
  {
    title: "Generate QR",
    description: "Quick guest access",
    icon: QrCode,
    variant: "default" as const,
    onClick: () => console.log("Generate QR")
  },
  {
    title: "Send Quote",
    description: "Client proposal",
    icon: Send,
    variant: "accent" as const,
    onClick: () => console.log("Send Quote")
  },
  {
    title: "Upload Photos",
    description: "Batch upload",
    icon: Upload,
    variant: "success" as const,
    onClick: () => console.log("Upload Photos")
  },
  {
    title: "Schedule Event",
    description: "Calendar booking",
    icon: Calendar,
    variant: "outline" as const,
    onClick: () => console.log("Schedule Event")
  },
  {
    title: "Add Client",
    description: "New client profile",
    icon: Users,
    variant: "outline" as const,
    onClick: () => console.log("Add Client")
  },
  {
    title: "Create Invoice",
    description: "Billing & payments",
    icon: FileText,
    variant: "outline" as const,
    onClick: () => console.log("Create Invoice")
  },
  {
    title: "Gallery Setup",
    description: "Organize albums",
    icon: Image,
    variant: "outline" as const,
    onClick: () => console.log("Gallery Setup")
  }
];

export function QuickActions() {
  return (
    <Card className="shadow-card border-border">
      <CardHeader>
        <CardTitle className="text-lg font-semibold">Quick Actions</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-3">
          {actions.map((action, index) => (
            <Button
              key={index}
              variant={action.variant}
              className="h-auto p-4 flex flex-col items-center gap-2 text-center group"
              onClick={action.onClick}
            >
              <action.icon className="h-5 w-5 group-hover:scale-110 transition-transform" />
              <div>
                <div className="font-medium text-sm">{action.title}</div>
                <div className="text-xs opacity-75">{action.description}</div>
              </div>
            </Button>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}