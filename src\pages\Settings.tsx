import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Textarea } from "@/components/ui/textarea";
import { Settings as SettingsIcon, Camera, Globe, Shield, Bell } from "lucide-react";
import { DashboardLayout } from "@/components/layout/DashboardLayout";

export default function Settings() {
  return (
    <DashboardLayout>
      <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold text-foreground mb-2">Settings</h1>
        <p className="text-muted-foreground">Manage your studio settings and preferences</p>
      </div>

      <div className="grid gap-6">
        <Card className="shadow-card border-border">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <SettingsIcon className="h-5 w-5" />
              General Settings
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="studioName">Studio Name</Label>
              <Input id="studioName" defaultValue="Om Digital Studio" />
            </div>
            <div>
              <Label htmlFor="ownerName">Owner Name</Label>
              <Input id="ownerName" defaultValue="Om Patel" />
            </div>
            <div>
              <Label htmlFor="address">Address</Label>
              <Textarea id="address" defaultValue="Lotus Mall, Shivaji Circle, near Reliance Point, Patel Nagar, Bardoli, Gujarat 394601" />
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="phone">Phone</Label>
                <Input id="phone" defaultValue="099790 49677" />
              </div>
              <div>
                <Label htmlFor="email">Email</Label>
                <Input id="email" defaultValue="<EMAIL>" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="shadow-card border-border">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Bell className="h-5 w-5" />
              Notification Settings
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <Label htmlFor="emailNotifications">Email Notifications</Label>
              <Switch id="emailNotifications" defaultChecked />
            </div>
            <div className="flex items-center justify-between">
              <Label htmlFor="smsNotifications">SMS Notifications</Label>
              <Switch id="smsNotifications" defaultChecked />
            </div>
            <div className="flex items-center justify-between">
              <Label htmlFor="whatsappNotifications">WhatsApp Notifications</Label>
              <Switch id="whatsappNotifications" defaultChecked />
            </div>
          </CardContent>
        </Card>

        <div className="flex gap-4">
          <Button>Save Changes</Button>
          <Button variant="outline">Reset to Default</Button>
        </div>
      </div>
      </div>
    </DashboardLayout>
  );
}