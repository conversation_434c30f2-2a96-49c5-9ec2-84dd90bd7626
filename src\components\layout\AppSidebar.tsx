import { useState } from "react";
import { NavLink, useLocation } from "react-router-dom";
import {
  Camera,
  LayoutDashboard,
  Calendar,
  Users,
  Settings,
  FileImage,
  ShoppingCart,
  CreditCard,
  UsersRound,
  Building,
  BarChart3,
  Bell,
  Globe,
  MessageSquare,
  ChevronDown,
} from "lucide-react";

import {
  Sidebar,
  SidebarContent,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  useSidebar,
  SidebarHeader,
} from "@/components/ui/sidebar";
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible";
import { Button } from "@/components/ui/button";
import { Logo } from "@/components/ui/logo";

const mainNavItems = [
  { title: "Dashboard", url: "/admin", icon: LayoutDashboard },
  { title: "Events", url: "/admin/events", icon: Calendar },
  { title: "Clients", url: "/admin/clients", icon: Users },
  { title: "Gallery", url: "/admin/gallery", icon: FileImage },
];

const businessNavItems = [
  { title: "Orders", url: "/admin/orders", icon: ShoppingCart },
  { title: "Quotes", url: "/admin/quotes", icon: CreditCard },
  { title: "Finance", url: "/admin/finance", icon: BarChart3 },
  { title: "Website", url: "/admin/website", icon: Globe },
];

const teamNavItems = [
  { title: "Freelancers", url: "/admin/freelancers", icon: UsersRound },
  { title: "Vendors", url: "/admin/vendors", icon: Building },
  { title: "Team", url: "/admin/team", icon: Users },
];

const systemNavItems = [
  { title: "Notifications", url: "/admin/notifications", icon: Bell },
  { title: "WhatsApp", url: "/admin/whatsapp", icon: MessageSquare },
  { title: "Settings", url: "/admin/settings", icon: Settings },
];

export function AppSidebar() {
  const { state } = useSidebar();
  const collapsed = state === "collapsed";
  const location = useLocation();
  const currentPath = location.pathname;
  
  const [expandedGroups, setExpandedGroups] = useState({
    business: true,
    team: false,
    system: false,
  });

  const isActive = (path: string) => currentPath === path;
  
  const getNavClassName = (path: string) =>
    `group flex items-center gap-3 px-4 py-3 rounded-xl transition-all duration-300 font-medium relative overflow-hidden ${
      isActive(path)
        ? "bg-gradient-to-r from-primary to-primary/80 text-white shadow-lg shadow-primary/25 scale-[0.98]"
        : "text-slate-600 hover:text-slate-900 hover:bg-white/60 hover:shadow-md hover:scale-[0.99] dark:text-slate-400 dark:hover:text-white dark:hover:bg-slate-800/60"
    }`;

  const toggleGroup = (group: string) => {
    setExpandedGroups(prev => ({
      ...prev,
      [group]: !prev[group as keyof typeof prev]
    }));
  };

  return (
    <Sidebar className={`${collapsed ? "w-16" : "w-72"} transition-all duration-500 ease-in-out border-r-0 bg-gradient-to-br from-slate-50 via-white to-slate-100/50 dark:from-slate-900 dark:via-slate-800 dark:to-slate-900/50 shadow-2xl shadow-slate-200/50 dark:shadow-slate-900/50`}>
      <SidebarHeader className="p-6 border-b border-slate-200/60 dark:border-slate-700/60 bg-white/40 dark:bg-slate-800/40 backdrop-blur-sm">
        {!collapsed && (
          <div className="space-y-3">
            <div className="flex items-center gap-3">
              <div className="p-2.5 bg-gradient-to-br from-primary to-primary/80 rounded-xl shadow-lg shadow-primary/25">
                <Logo size="sm" showText={false} />
              </div>
              <div>
                <h2 className="text-lg font-bold bg-gradient-to-r from-slate-800 to-slate-600 dark:from-white dark:to-slate-300 bg-clip-text text-transparent">
                  Om Studio
                </h2>
                <p className="text-xs text-slate-500 dark:text-slate-400 font-medium">Admin Dashboard</p>
              </div>
            </div>
          </div>
        )}
        {collapsed && (
          <div className="flex justify-center">
            <div className="p-2.5 bg-gradient-to-br from-primary to-primary/80 rounded-xl shadow-lg shadow-primary/25">
              <Logo size="sm" showText={false} />
            </div>
          </div>
        )}
      </SidebarHeader>

      <SidebarContent className="flex-1 overflow-auto py-6 px-4 space-y-8">
        {/* Main Navigation */}
        <div>
          <SidebarGroup>
            <SidebarGroupLabel className={collapsed ? "hidden" : "text-xs uppercase tracking-widest text-slate-500 dark:text-slate-400 mb-4 px-2 font-bold"}>
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-gradient-to-r from-primary to-primary/60 rounded-full"></div>
                Main Menu
              </div>
            </SidebarGroupLabel>
            <SidebarGroupContent>
              <SidebarMenu className="space-y-2">
                {mainNavItems.map((item) => (
                  <SidebarMenuItem key={item.title}>
                    <SidebarMenuButton asChild>
                      <NavLink to={item.url} className={getNavClassName(item.url)}>
                        <div className="relative">
                          <item.icon className="h-5 w-5 flex-shrink-0 relative z-10" />
                          {isActive(item.url) && (
                            <div className="absolute inset-0 bg-white/20 rounded-lg blur-sm"></div>
                          )}
                        </div>
                        {!collapsed && (
                          <span className="relative z-10 font-medium">{item.title}</span>
                        )}
                        {isActive(item.url) && !collapsed && (
                          <div className="ml-auto">
                            <div className="w-2 h-2 bg-white rounded-full animate-pulse"></div>
                          </div>
                        )}
                      </NavLink>
                    </SidebarMenuButton>
                  </SidebarMenuItem>
                ))}
              </SidebarMenu>
            </SidebarGroupContent>
          </SidebarGroup>
        </div>

        {/* Business Navigation */}
        <div>
          <SidebarGroup>
            <Collapsible
              open={expandedGroups.business}
              onOpenChange={() => toggleGroup('business')}
            >
              <CollapsibleTrigger asChild>
                <Button
                  variant="ghost"
                  className={`w-full justify-between p-3 h-auto hover:bg-white/60 dark:hover:bg-slate-800/60 rounded-xl transition-all duration-300 group ${collapsed ? "hidden" : ""}`}
                >
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-gradient-to-r from-blue-500 to-blue-400 rounded-full"></div>
                    <span className="text-xs uppercase tracking-widest text-slate-500 dark:text-slate-400 font-bold">
                      Business
                    </span>
                  </div>
                  <ChevronDown className={`h-4 w-4 transition-all duration-300 text-slate-400 group-hover:text-slate-600 dark:group-hover:text-slate-300 ${expandedGroups.business ? 'rotate-180' : ''}`} />
                </Button>
              </CollapsibleTrigger>
              <CollapsibleContent className="mt-3">
                <SidebarGroupContent>
                  <SidebarMenu className="space-y-2 pl-4">
                    {businessNavItems.map((item) => (
                      <SidebarMenuItem key={item.title}>
                        <SidebarMenuButton asChild>
                          <NavLink to={item.url} className={getNavClassName(item.url)}>
                            <div className="relative">
                              <item.icon className="h-5 w-5 flex-shrink-0 relative z-10" />
                              {isActive(item.url) && (
                                <div className="absolute inset-0 bg-white/20 rounded-lg blur-sm"></div>
                              )}
                            </div>
                            {!collapsed && (
                              <span className="relative z-10 font-medium">{item.title}</span>
                            )}
                            {isActive(item.url) && !collapsed && (
                              <div className="ml-auto">
                                <div className="w-2 h-2 bg-white rounded-full animate-pulse"></div>
                              </div>
                            )}
                          </NavLink>
                        </SidebarMenuButton>
                      </SidebarMenuItem>
                    ))}
                  </SidebarMenu>
                </SidebarGroupContent>
              </CollapsibleContent>
            </Collapsible>
          </SidebarGroup>
        </div>

        {/* Team Navigation */}
        <div>
          <SidebarGroup>
            <Collapsible
              open={expandedGroups.team}
              onOpenChange={() => toggleGroup('team')}
            >
              <CollapsibleTrigger asChild>
                <Button
                  variant="ghost"
                  className={`w-full justify-between p-3 h-auto hover:bg-white/60 dark:hover:bg-slate-800/60 rounded-xl transition-all duration-300 group ${collapsed ? "hidden" : ""}`}
                >
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-gradient-to-r from-green-500 to-green-400 rounded-full"></div>
                    <span className="text-xs uppercase tracking-widest text-slate-500 dark:text-slate-400 font-bold">
                      Team
                    </span>
                  </div>
                  <ChevronDown className={`h-4 w-4 transition-all duration-300 text-slate-400 group-hover:text-slate-600 dark:group-hover:text-slate-300 ${expandedGroups.team ? 'rotate-180' : ''}`} />
                </Button>
              </CollapsibleTrigger>
              <CollapsibleContent className="mt-3">
                <SidebarGroupContent>
                  <SidebarMenu className="space-y-2 pl-4">
                    {teamNavItems.map((item) => (
                      <SidebarMenuItem key={item.title}>
                        <SidebarMenuButton asChild>
                          <NavLink to={item.url} className={getNavClassName(item.url)}>
                            <div className="relative">
                              <item.icon className="h-5 w-5 flex-shrink-0 relative z-10" />
                              {isActive(item.url) && (
                                <div className="absolute inset-0 bg-white/20 rounded-lg blur-sm"></div>
                              )}
                            </div>
                            {!collapsed && (
                              <span className="relative z-10 font-medium">{item.title}</span>
                            )}
                            {isActive(item.url) && !collapsed && (
                              <div className="ml-auto">
                                <div className="w-2 h-2 bg-white rounded-full animate-pulse"></div>
                              </div>
                            )}
                          </NavLink>
                        </SidebarMenuButton>
                      </SidebarMenuItem>
                    ))}
                  </SidebarMenu>
                </SidebarGroupContent>
              </CollapsibleContent>
            </Collapsible>
          </SidebarGroup>
        </div>

        {/* System Navigation */}
        <div>
          <SidebarGroup>
            <Collapsible
              open={expandedGroups.system}
              onOpenChange={() => toggleGroup('system')}
            >
              <CollapsibleTrigger asChild>
                <Button
                  variant="ghost"
                  className={`w-full justify-between p-3 h-auto hover:bg-white/60 dark:hover:bg-slate-800/60 rounded-xl transition-all duration-300 group ${collapsed ? "hidden" : ""}`}
                >
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-gradient-to-r from-purple-500 to-purple-400 rounded-full"></div>
                    <span className="text-xs uppercase tracking-widest text-slate-500 dark:text-slate-400 font-bold">
                      System
                    </span>
                  </div>
                  <ChevronDown className={`h-4 w-4 transition-all duration-300 text-slate-400 group-hover:text-slate-600 dark:group-hover:text-slate-300 ${expandedGroups.system ? 'rotate-180' : ''}`} />
                </Button>
              </CollapsibleTrigger>
              <CollapsibleContent className="mt-3">
                <SidebarGroupContent>
                  <SidebarMenu className="space-y-2 pl-4">
                    {systemNavItems.map((item) => (
                      <SidebarMenuItem key={item.title}>
                        <SidebarMenuButton asChild>
                          <NavLink to={item.url} className={getNavClassName(item.url)}>
                            <div className="relative">
                              <item.icon className="h-5 w-5 flex-shrink-0 relative z-10" />
                              {isActive(item.url) && (
                                <div className="absolute inset-0 bg-white/20 rounded-lg blur-sm"></div>
                              )}
                            </div>
                            {!collapsed && (
                              <span className="relative z-10 font-medium">{item.title}</span>
                            )}
                            {isActive(item.url) && !collapsed && (
                              <div className="ml-auto">
                                <div className="w-2 h-2 bg-white rounded-full animate-pulse"></div>
                              </div>
                            )}
                          </NavLink>
                        </SidebarMenuButton>
                      </SidebarMenuItem>
                    ))}
                  </SidebarMenu>
                </SidebarGroupContent>
              </CollapsibleContent>
            </Collapsible>
          </SidebarGroup>
        </div>

        {/* Sidebar Footer */}
        {!collapsed && (
          <div className="mt-auto pt-6 border-t border-slate-200/60 dark:border-slate-700/60">
            <div className="px-4 py-3 bg-gradient-to-r from-slate-100/50 to-white/50 dark:from-slate-800/50 dark:to-slate-700/50 rounded-xl mx-4 mb-4">
              <div className="flex items-center gap-3">
                <div className="w-8 h-8 bg-gradient-to-br from-primary to-primary/80 rounded-lg flex items-center justify-center">
                  <span className="text-white text-xs font-bold">OS</span>
                </div>
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-slate-700 dark:text-slate-300 truncate">Om Studio</p>
                  <p className="text-xs text-slate-500 dark:text-slate-400">v2.0.1</p>
                </div>
                <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
              </div>
            </div>
          </div>
        )}
      </SidebarContent>
    </Sidebar>
  );
}