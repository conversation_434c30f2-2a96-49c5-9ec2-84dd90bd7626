import { useState } from "react";
import { NavLink, useLocation } from "react-router-dom";
import {
  Camera,
  LayoutDashboard,
  Calendar,
  Users,
  Settings,
  FileImage,
  ShoppingCart,
  CreditCard,
  UsersRound,
  Building,
  BarChart3,
  Bell,
  Globe,
  MessageSquare,
  ChevronDown,
} from "lucide-react";

import {
  Sidebar,
  SidebarContent,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  useSidebar,
  SidebarHeader,
} from "@/components/ui/sidebar";
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible";
import { Button } from "@/components/ui/button";
import { Logo } from "@/components/ui/logo";

const mainNavItems = [
  { title: "Dashboard", url: "/admin", icon: LayoutDashboard },
  { title: "Events", url: "/admin/events", icon: Calendar },
  { title: "Clients", url: "/admin/clients", icon: Users },
  { title: "Gallery", url: "/admin/gallery", icon: FileImage },
];

const businessNavItems = [
  { title: "Orders", url: "/admin/orders", icon: ShoppingCart },
  { title: "Quotes", url: "/admin/quotes", icon: CreditCard },
  { title: "Finance", url: "/admin/finance", icon: BarChart3 },
  { title: "Website", url: "/admin/website", icon: Globe },
];

const teamNavItems = [
  { title: "Freelancers", url: "/admin/freelancers", icon: UsersRound },
  { title: "Vendors", url: "/admin/vendors", icon: Building },
  { title: "Team", url: "/admin/team", icon: Users },
];

const systemNavItems = [
  { title: "Notifications", url: "/admin/notifications", icon: Bell },
  { title: "WhatsApp", url: "/admin/whatsapp", icon: MessageSquare },
  { title: "Settings", url: "/admin/settings", icon: Settings },
];

export function AppSidebar() {
  const { state } = useSidebar();
  const collapsed = state === "collapsed";
  const location = useLocation();
  const currentPath = location.pathname;
  
  const [expandedGroups, setExpandedGroups] = useState({
    business: true,
    team: false,
    system: false,
  });

  const isActive = (path: string) => currentPath === path;
  
  const getNavClassName = (path: string) =>
    `flex items-center gap-3 px-3 py-2.5 rounded-lg transition-all duration-200 font-medium ${
      isActive(path)
        ? "bg-primary text-primary-foreground shadow-sm"
        : "text-muted-foreground hover:text-foreground hover:bg-accent/50"
    }`;

  const toggleGroup = (group: string) => {
    setExpandedGroups(prev => ({
      ...prev,
      [group]: !prev[group as keyof typeof prev]
    }));
  };

  return (
    <Sidebar className={`${collapsed ? "w-16" : "w-64"} transition-all duration-300 border-r border-border bg-gradient-to-b from-card/80 to-card/60 backdrop-blur supports-[backdrop-filter]:bg-card/30 shadow-sm`}>
      <SidebarHeader className="p-4 border-b border-border/50">
        {!collapsed && (
          <div className="space-y-2">
            <Logo size="md" />
            <div className="px-2">
            </div>
          </div>
        )}
        {collapsed && (
          <div className="flex justify-center">
            <Logo size="sm" showText={false} />
          </div>
        )}
      </SidebarHeader>

      <SidebarContent className="flex-1 overflow-auto py-4">
        {/* Main Navigation */}
        <div className="px-4">
          <SidebarGroup>
            <SidebarGroupLabel className={collapsed ? "hidden" : "text-xs uppercase tracking-wider text-muted-foreground mb-3 px-2 font-semibold"}>
              Main
            </SidebarGroupLabel>
            <SidebarGroupContent>
              <SidebarMenu className="space-y-1">
                {mainNavItems.map((item) => (
                  <SidebarMenuItem key={item.title}>
                    <SidebarMenuButton asChild>
                      <NavLink to={item.url} className={getNavClassName(item.url)}>
                        <item.icon className="h-5 w-5 flex-shrink-0" />
                        {!collapsed && <span>{item.title}</span>}
                      </NavLink>
                    </SidebarMenuButton>
                  </SidebarMenuItem>
                ))}
              </SidebarMenu>
            </SidebarGroupContent>
          </SidebarGroup>
        </div>

        {/* Business Navigation */}
        <div className="px-4 mt-6">
          <SidebarGroup>
            <Collapsible
              open={expandedGroups.business}
              onOpenChange={() => toggleGroup('business')}
            >
              <CollapsibleTrigger asChild>
                <Button
                  variant="ghost"
                  className={`w-full justify-between p-2 h-auto hover:bg-accent/50 ${collapsed ? "hidden" : ""}`}
                >
                  <span className="text-xs uppercase tracking-wider text-muted-foreground font-semibold">
                    Business
                  </span>
                  <ChevronDown className={`h-4 w-4 transition-transform duration-200 ${expandedGroups.business ? 'rotate-180' : ''}`} />
                </Button>
              </CollapsibleTrigger>
              <CollapsibleContent className="mt-2">
                <SidebarGroupContent>
                  <SidebarMenu className="space-y-1">
                    {businessNavItems.map((item) => (
                      <SidebarMenuItem key={item.title}>
                        <SidebarMenuButton asChild>
                          <NavLink to={item.url} className={getNavClassName(item.url)}>
                            <item.icon className="h-5 w-5 flex-shrink-0" />
                            {!collapsed && <span>{item.title}</span>}
                          </NavLink>
                        </SidebarMenuButton>
                      </SidebarMenuItem>
                    ))}
                  </SidebarMenu>
                </SidebarGroupContent>
              </CollapsibleContent>
            </Collapsible>
          </SidebarGroup>
        </div>

        {/* Team Navigation */}
        <div className="px-4 mt-6">
          <SidebarGroup>
            <Collapsible
              open={expandedGroups.team}
              onOpenChange={() => toggleGroup('team')}
            >
              <CollapsibleTrigger asChild>
                <Button
                  variant="ghost"
                  className={`w-full justify-between p-2 h-auto hover:bg-accent/50 ${collapsed ? "hidden" : ""}`}
                >
                  <span className="text-xs uppercase tracking-wider text-muted-foreground font-semibold">
                    Team
                  </span>
                  <ChevronDown className={`h-4 w-4 transition-transform duration-200 ${expandedGroups.team ? 'rotate-180' : ''}`} />
                </Button>
              </CollapsibleTrigger>
              <CollapsibleContent className="mt-2">
                <SidebarGroupContent>
                  <SidebarMenu className="space-y-1">
                    {teamNavItems.map((item) => (
                      <SidebarMenuItem key={item.title}>
                        <SidebarMenuButton asChild>
                          <NavLink to={item.url} className={getNavClassName(item.url)}>
                            <item.icon className="h-5 w-5 flex-shrink-0" />
                            {!collapsed && <span>{item.title}</span>}
                          </NavLink>
                        </SidebarMenuButton>
                      </SidebarMenuItem>
                    ))}
                  </SidebarMenu>
                </SidebarGroupContent>
              </CollapsibleContent>
            </Collapsible>
          </SidebarGroup>
        </div>

        {/* System Navigation */}
        <div className="px-4 mt-6">
          <SidebarGroup>
            <Collapsible
              open={expandedGroups.system}
              onOpenChange={() => toggleGroup('system')}
            >
              <CollapsibleTrigger asChild>
                <Button
                  variant="ghost"
                  className={`w-full justify-between p-2 h-auto hover:bg-accent/50 ${collapsed ? "hidden" : ""}`}
                >
                  <span className="text-xs uppercase tracking-wider text-muted-foreground font-semibold">
                    System
                  </span>
                  <ChevronDown className={`h-4 w-4 transition-transform duration-200 ${expandedGroups.system ? 'rotate-180' : ''}`} />
                </Button>
              </CollapsibleTrigger>
              <CollapsibleContent className="mt-2">
                <SidebarGroupContent>
                  <SidebarMenu className="space-y-1">
                    {systemNavItems.map((item) => (
                      <SidebarMenuItem key={item.title}>
                        <SidebarMenuButton asChild>
                          <NavLink to={item.url} className={getNavClassName(item.url)}>
                            <item.icon className="h-5 w-5 flex-shrink-0" />
                            {!collapsed && <span>{item.title}</span>}
                          </NavLink>
                        </SidebarMenuButton>
                      </SidebarMenuItem>
                    ))}
                  </SidebarMenu>
                </SidebarGroupContent>
              </CollapsibleContent>
            </Collapsible>
          </SidebarGroup>
        </div>
      </SidebarContent>
    </Sidebar>
  );
}