import { useState } from "react";
import { NavLink, useLocation } from "react-router-dom";
import {
  Camera,
  LayoutDashboard,
  Calendar,
  Users,
  Settings,
  FileImage,
  ShoppingCart,
  CreditCard,
  UsersRound,
  Building,
  BarChart3,
  Bell,
  Globe,
  MessageSquare,
  ChevronDown,
  ChevronRight,
} from "lucide-react";

import {
  Sidebar,
  SidebarContent,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarTrigger,
  useSidebar,
  SidebarHeader,
} from "@/components/ui/sidebar";
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible";
import { Button } from "@/components/ui/button";

const mainNavItems = [
  { title: "Dashboard", url: "/", icon: LayoutDashboard },
  { title: "Events", url: "/events", icon: Calendar },
  { title: "Clients", url: "/clients", icon: Users },
  { title: "Gallery", url: "/gallery", icon: FileImage },
];

const businessNavItems = [
  { title: "Orders", url: "/orders", icon: ShoppingCart },
  { title: "Quotes", url: "/quotes", icon: CreditCard },
  { title: "Finance", url: "/finance", icon: BarChart3 },
  { title: "Website", url: "/website", icon: Globe },
];

const teamNavItems = [
  { title: "Freelancers", url: "/freelancers", icon: UsersRound },
  { title: "Vendors", url: "/vendors", icon: Building },
  { title: "Team", url: "/team", icon: Users },
];

const systemNavItems = [
  { title: "Notifications", url: "/notifications", icon: Bell },
  { title: "WhatsApp", url: "/whatsapp", icon: MessageSquare },
  { title: "Settings", url: "/settings", icon: Settings },
];

export function AppSidebar() {
  const { state } = useSidebar();
  const collapsed = state === "collapsed";
  const location = useLocation();
  const currentPath = location.pathname;
  
  const [expandedGroups, setExpandedGroups] = useState({
    business: true,
    team: false,
    system: false,
  });

  const isActive = (path: string) => currentPath === path;
  
  const getNavClassName = (path: string) =>
    `flex items-center gap-3 px-3 py-2 rounded-lg transition-smooth ${
      isActive(path)
        ? "bg-primary text-primary-foreground shadow-primary"
        : "text-muted-foreground hover:text-foreground hover:bg-accent"
    }`;

  const toggleGroup = (group: string) => {
    setExpandedGroups(prev => ({
      ...prev,
      [group]: !prev[group as keyof typeof prev]
    }));
  };

  return (
    <Sidebar className={`${collapsed ? "w-16" : "w-64"} transition-smooth border-r border-border`}>
      <SidebarHeader className="p-6 border-b border-border">
        {!collapsed && (
            <div className="flex items-center gap-3">
              <img 
                src="/src/assets/om-digital-studio-logo.png" 
                alt="Om Digital Studio" 
                className="h-10 w-auto"
              />
              <div>
                <h2 className="text-lg font-bold text-foreground">
                  Om Digital Studio
                </h2>
                <p className="text-sm text-muted-foreground">Admin Panel</p>
              </div>
            </div>
        )}
        {collapsed && (
          <div className="flex justify-center">
            <img 
              src="/src/assets/om-digital-studio-logo.png" 
              alt="Om Digital Studio" 
              className="h-8 w-auto"
            />
          </div>
        )}
      </SidebarHeader>

      <SidebarContent className="p-4">
        {/* Main Navigation */}
        <SidebarGroup>
          <SidebarGroupLabel className={collapsed ? "hidden" : "text-xs uppercase tracking-wider text-muted-foreground mb-2"}>
            Main
          </SidebarGroupLabel>
          <SidebarGroupContent>
            <SidebarMenu className="space-y-1">
              {mainNavItems.map((item) => (
                <SidebarMenuItem key={item.title}>
                  <SidebarMenuButton asChild>
                    <NavLink to={item.url} className={getNavClassName(item.url)}>
                      <item.icon className="h-5 w-5" />
                      {!collapsed && <span className="font-medium">{item.title}</span>}
                    </NavLink>
                  </SidebarMenuButton>
                </SidebarMenuItem>
              ))}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>

        {/* Business Navigation */}
        <SidebarGroup>
          <Collapsible
            open={expandedGroups.business}
            onOpenChange={() => toggleGroup('business')}
          >
            <CollapsibleTrigger asChild>
              <Button
                variant="ghost"
                className={`w-full justify-between p-0 h-auto mb-2 ${collapsed ? "hidden" : ""}`}
              >
                <span className="text-xs uppercase tracking-wider text-muted-foreground">
                  Business
                </span>
                {expandedGroups.business ? (
                  <ChevronDown className="h-4 w-4" />
                ) : (
                  <ChevronRight className="h-4 w-4" />
                )}
              </Button>
            </CollapsibleTrigger>
            <CollapsibleContent>
              <SidebarGroupContent>
                <SidebarMenu className="space-y-1">
                  {businessNavItems.map((item) => (
                    <SidebarMenuItem key={item.title}>
                      <SidebarMenuButton asChild>
                        <NavLink to={item.url} className={getNavClassName(item.url)}>
                          <item.icon className="h-5 w-5" />
                          {!collapsed && <span className="font-medium">{item.title}</span>}
                        </NavLink>
                      </SidebarMenuButton>
                    </SidebarMenuItem>
                  ))}
                </SidebarMenu>
              </SidebarGroupContent>
            </CollapsibleContent>
          </Collapsible>
        </SidebarGroup>

        {/* Team Navigation */}
        <SidebarGroup>
          <Collapsible
            open={expandedGroups.team}
            onOpenChange={() => toggleGroup('team')}
          >
            <CollapsibleTrigger asChild>
              <Button
                variant="ghost"
                className={`w-full justify-between p-0 h-auto mb-2 ${collapsed ? "hidden" : ""}`}
              >
                <span className="text-xs uppercase tracking-wider text-muted-foreground">
                  Team
                </span>
                {expandedGroups.team ? (
                  <ChevronDown className="h-4 w-4" />
                ) : (
                  <ChevronRight className="h-4 w-4" />
                )}
              </Button>
            </CollapsibleTrigger>
            <CollapsibleContent>
              <SidebarGroupContent>
                <SidebarMenu className="space-y-1">
                  {teamNavItems.map((item) => (
                    <SidebarMenuItem key={item.title}>
                      <SidebarMenuButton asChild>
                        <NavLink to={item.url} className={getNavClassName(item.url)}>
                          <item.icon className="h-5 w-5" />
                          {!collapsed && <span className="font-medium">{item.title}</span>}
                        </NavLink>
                      </SidebarMenuButton>
                    </SidebarMenuItem>
                  ))}
                </SidebarMenu>
              </SidebarGroupContent>
            </CollapsibleContent>
          </Collapsible>
        </SidebarGroup>

        {/* System Navigation */}
        <SidebarGroup>
          <Collapsible
            open={expandedGroups.system}
            onOpenChange={() => toggleGroup('system')}
          >
            <CollapsibleTrigger asChild>
              <Button
                variant="ghost"
                className={`w-full justify-between p-0 h-auto mb-2 ${collapsed ? "hidden" : ""}`}
              >
                <span className="text-xs uppercase tracking-wider text-muted-foreground">
                  System
                </span>
                {expandedGroups.system ? (
                  <ChevronDown className="h-4 w-4" />
                ) : (
                  <ChevronRight className="h-4 w-4" />
                )}
              </Button>
            </CollapsibleTrigger>
            <CollapsibleContent>
              <SidebarGroupContent>
                <SidebarMenu className="space-y-1">
                  {systemNavItems.map((item) => (
                    <SidebarMenuItem key={item.title}>
                      <SidebarMenuButton asChild>
                        <NavLink to={item.url} className={getNavClassName(item.url)}>
                          <item.icon className="h-5 w-5" />
                          {!collapsed && <span className="font-medium">{item.title}</span>}
                        </NavLink>
                      </SidebarMenuButton>
                    </SidebarMenuItem>
                  ))}
                </SidebarMenu>
              </SidebarGroupContent>
            </CollapsibleContent>
          </Collapsible>
        </SidebarGroup>
      </SidebarContent>
    </Sidebar>
  );
}