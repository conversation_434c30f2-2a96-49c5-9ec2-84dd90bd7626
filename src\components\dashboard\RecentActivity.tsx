import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>itle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { 
  Upload, 
  Download, 
  UserPlus, 
  Calendar,
  CreditCard,
  Image,
  Users,
  Clock
} from "lucide-react";

const activities = [
  {
    id: 1,
    type: "upload",
    title: "250 photos uploaded",
    description: "<PERSON> & John Wedding - Reception",
    time: "2 minutes ago",
    icon: Upload,
    status: "success",
    user: { name: "Alex Studio", avatar: "/placeholder-avatar.jpg" }
  },
  {
    id: 2,
    type: "download",
    title: "Gallery download",
    description: "Client downloaded 45 high-res photos",
    time: "15 minutes ago",
    icon: Download,
    status: "info",
    user: { name: "<PERSON>", avatar: "/placeholder-avatar.jpg" }
  },
  {
    id: 3,
    type: "client",
    title: "New client inquiry",
    description: "Corporate event photography request",
    time: "1 hour ago",
    icon: UserPlus,
    status: "pending",
    user: { name: "TechCorp Ltd", avatar: "/placeholder-avatar.jpg" }
  },
  {
    id: 4,
    type: "event",
    title: "Event scheduled",
    description: "Birthday celebration shoot - Dec 25",
    time: "2 hours ago",
    icon: Calendar,
    status: "scheduled",
    user: { name: "Emma <PERSON>", avatar: "/placeholder-avatar.jpg" }
  },
  {
    id: 5,
    type: "payment",
    title: "Payment received",
    description: "₹25,000 for wedding package",
    time: "3 hours ago",
    icon: CreditCard,
    status: "success",
    user: { name: "Mike & Lisa", avatar: "/placeholder-avatar.jpg" }
  },
  {
    id: 6,
    type: "approval",
    title: "Guest photos pending",
    description: "12 guest uploads require approval",
    time: "4 hours ago",
    icon: Image,
    status: "warning",
    user: { name: "Wedding Guests", avatar: "/placeholder-avatar.jpg" }
  }
];

const statusStyles = {
  success: "bg-success/10 text-success border-success/20",
  info: "bg-accent/10 text-accent border-accent/20", 
  pending: "bg-warning/10 text-warning border-warning/20",
  scheduled: "bg-primary/10 text-primary border-primary/20",
  warning: "bg-destructive/10 text-destructive border-destructive/20"
};

export function RecentActivity() {
  return (
    <Card className="shadow-card border-border">
      <CardHeader>
        <CardTitle className="text-lg font-semibold flex items-center gap-2">
          <Clock className="h-5 w-5 text-primary" />
          Recent Activity
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {activities.map((activity) => (
            <div
              key={activity.id}
              className="flex items-start gap-3 p-3 rounded-lg hover:bg-accent/50 transition-smooth cursor-pointer"
            >
              <div className="p-2 bg-gradient-secondary rounded-lg">
                <activity.icon className="h-4 w-4 text-primary" />
              </div>
              
              <div className="flex-1 min-w-0">
                <div className="flex items-center gap-2 mb-1">
                  <h4 className="font-medium text-sm text-foreground">
                    {activity.title}
                  </h4>
                  <Badge 
                    variant="outline" 
                    className={statusStyles[activity.status as keyof typeof statusStyles]}
                  >
                    {activity.status}
                  </Badge>
                </div>
                <p className="text-sm text-muted-foreground mb-1">
                  {activity.description}
                </p>
                <div className="flex items-center gap-2">
                  <Avatar className="h-5 w-5">
                    <AvatarImage src={activity.user.avatar} alt={activity.user.name} />
                    <AvatarFallback className="bg-gradient-primary text-white text-xs">
                      {activity.user.name.charAt(0)}
                    </AvatarFallback>
                  </Avatar>
                  <span className="text-xs text-muted-foreground">
                    {activity.user.name} • {activity.time}
                  </span>
                </div>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}