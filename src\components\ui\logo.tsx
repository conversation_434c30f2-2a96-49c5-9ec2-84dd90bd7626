import { Camera } from "lucide-react";

interface LogoProps {
  size?: "sm" | "md" | "lg";
  showText?: boolean;
  className?: string;
}

export function Logo({ size = "md", showText = true, className = "" }: LogoProps) {
  const sizeClasses = {
    sm: "h-6 w-6",
    md: "h-8 w-8", 
    lg: "h-12 w-12"
  };

  const textSizeClasses = {
    sm: "text-lg",
    md: "text-xl",
    lg: "text-2xl"
  };

  return (
    <div className={`flex items-center gap-3 ${className}`}>
      <div className="p-2 bg-primary/10 rounded-lg">
        <Camera className={`${sizeClasses[size]} text-primary`} />
      </div>
      {showText && (
        <div>
          <h2 className={`${textSizeClasses[size]} font-bold text-foreground`}>
            Om Digital Studio
          </h2>
          <p className="text-sm text-muted-foreground">Capture Your Memories</p>
        </div>
      )}
    </div>
  );
}
