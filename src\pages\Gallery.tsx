import { DashboardLayout } from "@/components/layout/DashboardLayout";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { 
  Plus,
  Search,
  Filter,
  Image,
  Download,
  Share,
  Eye,
  Grid,
  List,
  Upload,
  FolderOpen,
  Users,
  Calendar,
  MoreVertical
} from "lucide-react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import eventCollage from "@/assets/event-collage.jpg";

const albums = [
  {
    id: 1,
    title: "Sarah & John Wedding",
    eventDate: "Dec 15, 2024",
    photos: 847,
    downloads: 245,
    views: 1250,
    type: "Wedding",
    status: "published",
    thumbnail: eventCollage,
    privacy: "private",
    lastUpdated: "2 hours ago",
    photographer: "Alex Studio"
  },
  {
    id: 2,
    title: "TechCorp Annual Meet",
    eventDate: "Dec 18, 2024",
    photos: 0,
    downloads: 0,
    views: 0,
    type: "Corporate",
    status: "draft",
    thumbnail: eventCollage,
    privacy: "private",
    lastUpdated: "1 day ago",
    photographer: "Mike Photo"
  },
  {
    id: 3,
    title: "Mumbai Fashion Week",
    eventDate: "Nov 28, 2024",
    photos: 1250,
    downloads: 578,
    views: 3450,
    type: "Fashion",
    status: "published",
    thumbnail: eventCollage,
    privacy: "public",
    lastUpdated: "1 week ago",
    photographer: "Pro Studios"
  },
  {
    id: 4,
    title: "Anniversary Celebration",
    eventDate: "Nov 15, 2024",
    photos: 425,
    downloads: 156,
    views: 890,
    type: "Anniversary",
    status: "published",
    thumbnail: eventCollage,
    privacy: "private",
    lastUpdated: "2 weeks ago",
    photographer: "Romantic Shots"
  },
  {
    id: 5,
    title: "Product Launch Event",
    eventDate: "Dec 28, 2024",
    photos: 0,
    downloads: 0,
    views: 0,
    type: "Corporate",
    status: "pending",
    thumbnail: eventCollage,
    privacy: "private",
    lastUpdated: "3 days ago",
    photographer: "Alex Studio"
  },
  {
    id: 6,
    title: "Birthday Celebration",
    eventDate: "Nov 10, 2024",
    photos: 320,
    downloads: 89,
    views: 456,
    type: "Birthday",
    status: "published",
    thumbnail: eventCollage,
    privacy: "friends",
    lastUpdated: "3 weeks ago",
    photographer: "Party Shots"
  }
];

const statusStyles = {
  published: "bg-success/10 text-success border-success/20",
  draft: "bg-warning/10 text-warning border-warning/20",
  pending: "bg-accent/10 text-accent border-accent/20",
  archived: "bg-muted/10 text-muted-foreground border-muted/20"
};

const privacyStyles = {
  public: "bg-blue-500/10 text-blue-600 border-blue-500/20",
  private: "bg-red-500/10 text-red-600 border-red-500/20",
  friends: "bg-green-500/10 text-green-600 border-green-500/20"
};

const typeStyles = {
  Wedding: "bg-pink-500/10 text-pink-600 border-pink-500/20",
  Corporate: "bg-blue-500/10 text-blue-600 border-blue-500/20",
  Birthday: "bg-purple-500/10 text-purple-600 border-purple-500/20",
  Fashion: "bg-orange-500/10 text-orange-600 border-orange-500/20",
  Anniversary: "bg-red-500/10 text-red-600 border-red-500/20"
};

export default function Gallery() {
  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row justify-between items-start gap-4">
          <div>
            <h1 className="text-3xl font-bold bg-gradient-primary bg-clip-text text-transparent">
              Gallery Management
            </h1>
            <p className="text-muted-foreground">
              Organize, share and manage your photography collections
            </p>
          </div>
          <div className="flex gap-2">
            <Button variant="outline">
              <Upload className="h-4 w-4 mr-2" />
              Bulk Upload
            </Button>
            <Button variant="gradient">
              <Plus className="h-4 w-4 mr-2" />
              Create Album
            </Button>
          </div>
        </div>

        {/* Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card className="shadow-card border-border">
            <CardContent className="p-4 text-center">
              <div className="text-2xl font-bold text-foreground">2,842</div>
              <div className="text-sm text-muted-foreground">Total Photos</div>
            </CardContent>
          </Card>
          <Card className="shadow-card border-border">
            <CardContent className="p-4 text-center">
              <div className="text-2xl font-bold text-primary">12</div>
              <div className="text-sm text-muted-foreground">Active Albums</div>
            </CardContent>
          </Card>
          <Card className="shadow-card border-border">
            <CardContent className="p-4 text-center">
              <div className="text-2xl font-bold text-success">1,068</div>
              <div className="text-sm text-muted-foreground">Downloads</div>
            </CardContent>
          </Card>
          <Card className="shadow-card border-border">
            <CardContent className="p-4 text-center">
              <div className="text-2xl font-bold text-accent">6,135</div>
              <div className="text-sm text-muted-foreground">Total Views</div>
            </CardContent>
          </Card>
        </div>

        {/* Filters */}
        <Card className="shadow-card border-border">
          <CardContent className="p-4">
            <div className="flex flex-col sm:flex-row gap-4 items-center">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search albums..."
                  className="pl-10"
                />
              </div>
              <div className="flex gap-2">
                <Select>
                  <SelectTrigger className="w-[150px]">
                    <SelectValue placeholder="Status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Status</SelectItem>
                    <SelectItem value="published">Published</SelectItem>
                    <SelectItem value="draft">Draft</SelectItem>
                    <SelectItem value="pending">Pending</SelectItem>
                  </SelectContent>
                </Select>
                <Select>
                  <SelectTrigger className="w-[150px]">
                    <SelectValue placeholder="Type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Types</SelectItem>
                    <SelectItem value="wedding">Wedding</SelectItem>
                    <SelectItem value="corporate">Corporate</SelectItem>
                    <SelectItem value="fashion">Fashion</SelectItem>
                  </SelectContent>
                </Select>
                <Button variant="outline">
                  <Filter className="h-4 w-4 mr-2" />
                  Filters
                </Button>
                <div className="flex border border-border rounded-lg">
                  <Button variant="ghost" size="sm" className="rounded-r-none border-r">
                    <Grid className="h-4 w-4" />
                  </Button>
                  <Button variant="ghost" size="sm" className="rounded-l-none">
                    <List className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Albums Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {albums.map((album) => (
            <Card key={album.id} className="shadow-card border-border hover:shadow-primary transition-smooth group">
              <div className="relative h-48 overflow-hidden rounded-t-lg">
                <img
                  src={album.thumbnail}
                  alt={album.title}
                  className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent" />
                <div className="absolute top-3 left-3 flex gap-2">
                  <Badge 
                    variant="outline" 
                    className={statusStyles[album.status as keyof typeof statusStyles]}
                  >
                    {album.status}
                  </Badge>
                  <Badge 
                    variant="outline" 
                    className={privacyStyles[album.privacy as keyof typeof privacyStyles]}
                  >
                    {album.privacy}
                  </Badge>
                </div>
                <div className="absolute top-3 right-3">
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="icon" className="bg-black/20 hover:bg-black/40 text-white">
                        <MoreVertical className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem>
                        <Eye className="h-4 w-4 mr-2" />
                        View Album
                      </DropdownMenuItem>
                      <DropdownMenuItem>
                        <Share className="h-4 w-4 mr-2" />
                        Share Gallery
                      </DropdownMenuItem>
                      <DropdownMenuItem>
                        <Download className="h-4 w-4 mr-2" />
                        Download All
                      </DropdownMenuItem>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem>
                        <Upload className="h-4 w-4 mr-2" />
                        Add Photos
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
                <div className="absolute bottom-3 left-3 right-3">
                  <Badge 
                    variant="outline" 
                    className={typeStyles[album.type as keyof typeof typeStyles]}
                  >
                    {album.type}
                  </Badge>
                  <h3 className="text-white font-semibold mt-2 line-clamp-1">
                    {album.title}
                  </h3>
                </div>
              </div>

              <CardContent className="p-4">
                <div className="space-y-3">
                  <div className="flex items-center gap-4 text-sm text-muted-foreground">
                    <div className="flex items-center gap-1">
                      <Calendar className="h-4 w-4" />
                      {album.eventDate}
                    </div>
                    <div className="flex items-center gap-1">
                      <Users className="h-4 w-4" />
                      {album.photographer}
                    </div>
                  </div>

                  <div className="grid grid-cols-3 gap-4 py-2 border-t border-border">
                    <div className="text-center">
                      <div className="text-lg font-semibold text-foreground">{album.photos}</div>
                      <div className="text-xs text-muted-foreground">Photos</div>
                    </div>
                    <div className="text-center">
                      <div className="text-lg font-semibold text-foreground">{album.downloads}</div>
                      <div className="text-xs text-muted-foreground">Downloads</div>
                    </div>
                    <div className="text-center">
                      <div className="text-lg font-semibold text-foreground">{album.views}</div>
                      <div className="text-xs text-muted-foreground">Views</div>
                    </div>
                  </div>

                  <div className="flex items-center justify-between pt-2 border-t border-border text-sm text-muted-foreground">
                    <span>Updated {album.lastUpdated}</span>
                    <Button variant="outline" size="sm">
                      <FolderOpen className="h-4 w-4 mr-1" />
                      Open
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </DashboardLayout>
  );
}