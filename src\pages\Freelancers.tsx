import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Textarea } from "@/components/ui/textarea";
import { 
  Users, 
  Search, 
  Plus, 
  Eye,
  Edit,
  Star,
  MapPin,
  Phone,
  Mail,
  Camera,
  Video,
  Palette,
  Monitor,
  CheckCircle,
  XCircle,
  Clock
} from "lucide-react";
import { DashboardLayout } from "@/components/layout/DashboardLayout";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";

const freelancers = [
  {
    id: 1,
    name: "<PERSON><PERSON><PERSON>",
    email: "<EMAIL>",
    phone: "9876543210",
    skills: ["Wedding Photography", "Portrait"],
    experience: "5 years",
    location: "Mumbai, Maharashtra",
    rating: 4.8,
    totalJobs: 45,
    status: "available",
    hourlyRate: "₹2,500",
    joinDate: "2023-06-15",
    avatar: "/placeholder-avatar.jpg"
  },
  {
    id: 2,
    name: "Priya Desai",
    email: "<EMAIL>", 
    phone: "9876543211",
    skills: ["Event Photography", "Videography"],
    experience: "3 years",
    location: "Ahmedabad, Gujarat",
    rating: 4.9,
    totalJobs: 32,
    status: "busy",
    hourlyRate: "₹2,000",
    joinDate: "2023-08-20",
    avatar: "/placeholder-avatar.jpg"
  },
  {
    id: 3,
    name: "Rahul Patel",
    email: "<EMAIL>",
    phone: "9876543212",
    skills: ["Photo Editing", "Color Grading"],
    experience: "4 years", 
    location: "Pune, Maharashtra",
    rating: 4.7,
    totalJobs: 28,
    status: "available",
    hourlyRate: "₹1,800",
    joinDate: "2023-04-10",
    avatar: "/placeholder-avatar.jpg"
  },
  {
    id: 4,
    name: "Sneha Sharma",
    email: "<EMAIL>",
    phone: "9876543213",
    skills: ["Baby Photography", "Family Portraits"],
    experience: "6 years",
    location: "Surat, Gujarat",
    rating: 4.9,
    totalJobs: 67,
    status: "available",
    hourlyRate: "₹2,200",
    joinDate: "2023-01-05",
    avatar: "/placeholder-avatar.jpg"
  },
  {
    id: 5,
    name: "Kiran Modi",
    email: "<EMAIL>",
    phone: "9876543214",
    skills: ["Corporate Events", "Product Photography"],
    experience: "7 years",
    location: "Bardoli, Gujarat",
    rating: 4.6,
    totalJobs: 89,
    status: "on_leave",
    hourlyRate: "₹3,000",
    joinDate: "2022-11-15",
    avatar: "/placeholder-avatar.jpg"
  }
];

const getStatusColor = (status: string) => {
  switch (status) {
    case "available": return "bg-success/10 text-success border-success/20";
    case "busy": return "bg-warning/10 text-warning border-warning/20";
    case "on_leave": return "bg-destructive/10 text-destructive border-destructive/20";
    default: return "bg-muted/10 text-muted-foreground border-muted/20";
  }
};

const getSkillIcon = (skill: string) => {
  if (skill.includes("Photography")) return Camera;
  if (skill.includes("Video")) return Video;
  if (skill.includes("Edit")) return Palette;
  return Monitor;
};

export default function Freelancers() {
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const [showAddForm, setShowAddForm] = useState(false);

  const filteredFreelancers = freelancers.filter(freelancer => {
    const matchesSearch = freelancer.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         freelancer.skills.some(skill => skill.toLowerCase().includes(searchTerm.toLowerCase()));
    const matchesStatus = statusFilter === "all" || freelancer.status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  const availableCount = freelancers.filter(f => f.status === "available").length;
  const busyCount = freelancers.filter(f => f.status === "busy").length;
  const avgRating = freelancers.reduce((sum, f) => sum + f.rating, 0) / freelancers.length;

  return (
    <DashboardLayout>
      <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col lg:flex-row gap-4 justify-between items-start lg:items-center">
        <div>
          <h1 className="text-3xl font-bold text-foreground mb-2">
            Freelancers Management
          </h1>
          <p className="text-muted-foreground">
            Manage your network of freelance photographers and editors
          </p>
        </div>
        <Button className="gap-2" onClick={() => setShowAddForm(true)}>
          <Plus className="h-4 w-4" />
          Add Freelancer
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card className="shadow-card border-border">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Freelancers</p>
                <p className="text-2xl font-bold">{freelancers.length}</p>
              </div>
              <Users className="h-8 w-8 text-primary" />
            </div>
            <p className="text-xs text-muted-foreground mt-2">Active network</p>
          </CardContent>
        </Card>

        <Card className="shadow-card border-border">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Available</p>
                <p className="text-2xl font-bold">{availableCount}</p>
              </div>
              <CheckCircle className="h-8 w-8 text-success" />
            </div>
            <p className="text-xs text-muted-foreground mt-2">Ready for work</p>
          </CardContent>
        </Card>

        <Card className="shadow-card border-border">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Currently Busy</p>
                <p className="text-2xl font-bold">{busyCount}</p>
              </div>
              <Clock className="h-8 w-8 text-warning" />
            </div>
            <p className="text-xs text-muted-foreground mt-2">On assignments</p>
          </CardContent>
        </Card>

        <Card className="shadow-card border-border">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Average Rating</p>
                <p className="text-2xl font-bold">{avgRating.toFixed(1)}</p>
              </div>
              <Star className="h-8 w-8 text-accent" />
            </div>
            <p className="text-xs text-muted-foreground mt-2">Team quality score</p>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card className="shadow-card border-border">
        <CardContent className="p-6">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search freelancers by name or skills..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-full md:w-40">
                <SelectValue placeholder="Filter status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="available">Available</SelectItem>
                <SelectItem value="busy">Busy</SelectItem>
                <SelectItem value="on_leave">On Leave</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Freelancers Table */}
      <Card className="shadow-card border-border">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            Freelancer Network
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Freelancer</TableHead>
                  <TableHead>Skills</TableHead>
                  <TableHead>Experience</TableHead>
                  <TableHead>Rating</TableHead>
                  <TableHead>Rate</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredFreelancers.map((freelancer) => (
                  <TableRow key={freelancer.id}>
                    <TableCell>
                      <div className="flex items-center gap-3">
                        <Avatar className="h-10 w-10">
                          <AvatarImage src={freelancer.avatar} alt={freelancer.name} />
                          <AvatarFallback>
                            {freelancer.name.split(' ').map(n => n[0]).join('')}
                          </AvatarFallback>
                        </Avatar>
                        <div>
                          <p className="font-medium">{freelancer.name}</p>
                          <div className="flex items-center gap-1 text-sm text-muted-foreground">
                            <MapPin className="h-3 w-3" />
                            {freelancer.location}
                          </div>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex flex-wrap gap-1">
                        {freelancer.skills.slice(0, 2).map((skill, index) => {
                          const SkillIcon = getSkillIcon(skill);
                          return (
                            <Badge key={index} variant="outline" className="gap-1">
                              <SkillIcon className="h-3 w-3" />
                              {skill}
                            </Badge>
                          );
                        })}
                        {freelancer.skills.length > 2 && (
                          <Badge variant="outline">+{freelancer.skills.length - 2}</Badge>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>{freelancer.experience}</TableCell>
                    <TableCell>
                      <div className="flex items-center gap-1">
                        <Star className="h-4 w-4 text-warning fill-current" />
                        <span className="font-medium">{freelancer.rating}</span>
                        <span className="text-sm text-muted-foreground">({freelancer.totalJobs})</span>
                      </div>
                    </TableCell>
                    <TableCell className="font-medium">{freelancer.hourlyRate}/hr</TableCell>
                    <TableCell>
                      <Badge variant="outline" className={getStatusColor(freelancer.status)}>
                        {freelancer.status.replace('_', ' ')}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <div className="flex gap-2">
                        <Dialog>
                          <DialogTrigger asChild>
                            <Button variant="outline" size="sm">
                              <Eye className="h-4 w-4" />
                            </Button>
                          </DialogTrigger>
                          <DialogContent className="max-w-2xl">
                            <DialogHeader>
                              <DialogTitle>Freelancer Profile - {freelancer.name}</DialogTitle>
                              <DialogDescription>
                                Complete freelancer information and project history
                              </DialogDescription>
                            </DialogHeader>
                            <div className="space-y-4">
                              <div className="flex items-center gap-4">
                                <Avatar className="h-16 w-16">
                                  <AvatarImage src={freelancer.avatar} alt={freelancer.name} />
                                  <AvatarFallback className="text-lg">
                                    {freelancer.name.split(' ').map(n => n[0]).join('')}
                                  </AvatarFallback>
                                </Avatar>
                                <div>
                                  <h3 className="text-lg font-semibold">{freelancer.name}</h3>
                                  <div className="flex items-center gap-4 text-sm text-muted-foreground">
                                    <span className="flex items-center gap-1">
                                      <Mail className="h-3 w-3" />
                                      {freelancer.email}
                                    </span>
                                    <span className="flex items-center gap-1">
                                      <Phone className="h-3 w-3" />
                                      {freelancer.phone}
                                    </span>
                                  </div>
                                </div>
                              </div>
                              <div className="grid grid-cols-2 gap-4">
                                <div>
                                  <label className="text-sm font-medium">Experience</label>
                                  <p className="text-sm text-muted-foreground">{freelancer.experience}</p>
                                </div>
                                <div>
                                  <label className="text-sm font-medium">Hourly Rate</label>
                                  <p className="text-sm text-muted-foreground">{freelancer.hourlyRate}</p>
                                </div>
                              </div>
                              <div>
                                <label className="text-sm font-medium">Skills & Expertise</label>
                                <div className="flex flex-wrap gap-2 mt-2">
                                  {freelancer.skills.map((skill, index) => (
                                    <Badge key={index} variant="outline">{skill}</Badge>
                                  ))}
                                </div>
                              </div>
                              <div className="flex gap-2 mt-4">
                                <Button variant="outline">Edit Profile</Button>
                                <Button variant="outline">Assign Project</Button>
                                <Button>Send Message</Button>
                              </div>
                            </div>
                          </DialogContent>
                        </Dialog>
                        <Button variant="outline" size="sm">
                          <Edit className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>

      {/* Add Freelancer Dialog */}
      <Dialog open={showAddForm} onOpenChange={setShowAddForm}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Add New Freelancer</DialogTitle>
            <DialogDescription>
              Add a freelancer to your network
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="name">Full Name</Label>
                <Input id="name" placeholder="Enter freelancer name" />
              </div>
              <div>
                <Label htmlFor="email">Email</Label>
                <Input id="email" type="email" placeholder="<EMAIL>" />
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="phone">Phone Number</Label>
                <Input id="phone" placeholder="Enter phone number" />
              </div>
              <div>
                <Label htmlFor="location">Location</Label>
                <Input id="location" placeholder="City, State" />
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="experience">Experience</Label>
                <Select>
                  <SelectTrigger>
                    <SelectValue placeholder="Years of experience" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="1-2">1-2 years</SelectItem>
                    <SelectItem value="3-5">3-5 years</SelectItem>
                    <SelectItem value="5+">5+ years</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label htmlFor="hourlyRate">Hourly Rate</Label>
                <Input id="hourlyRate" placeholder="₹0" />
              </div>
            </div>
            <div>
              <Label htmlFor="skills">Skills & Expertise</Label>
              <Textarea id="skills" placeholder="List skills separated by commas..." />
            </div>
            <div className="flex gap-2 mt-6">
              <Button variant="outline" onClick={() => setShowAddForm(false)}>
                Cancel
              </Button>
              <Button onClick={() => setShowAddForm(false)}>
                Add Freelancer
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
      </div>
    </DashboardLayout>
  );
}