import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON><PERSON>, Star, Heart } from "lucide-react";

const products = [
  {
    id: 1,
    name: "Premium Photo Album",
    description: "Handcrafted leather-bound photo album with 50 pages",
    price: 2999,
    originalPrice: 3999,
    image: "/src/assets/studio-hero.jpg",
    rating: 4.8,
    category: "Albums"
  },
  {
    id: 2,
    name: "Canvas Print (16x20)",
    description: "High-quality canvas print on premium material",
    price: 1499,
    originalPrice: 1999,
    image: "/src/assets/event-collage.jpg",
    rating: 4.9,
    category: "Prints"
  },
  {
    id: 3,
    name: "Digital Photo Package",
    description: "High-resolution digital photos with editing",
    price: 999,
    originalPrice: 1299,
    image: "/src/assets/studio-hero.jpg",
    rating: 4.7,
    category: "Digital"
  },
  {
    id: 4,
    name: "Wedding Photo Frame",
    description: "Elegant photo frame perfect for wedding photos",
    price: 1299,
    originalPrice: 1599,
    image: "/src/assets/event-collage.jpg",
    rating: 4.6,
    category: "Frames"
  },
  {
    id: 5,
    name: "Photo Calendar 2024",
    description: "Personalized calendar with your favorite photos",
    price: 599,
    originalPrice: 799,
    image: "/src/assets/studio-hero.jpg",
    rating: 4.5,
    category: "Calendars"
  },
  {
    id: 6,
    name: "Photo Editing Service",
    description: "Professional photo editing and enhancement",
    price: 199,
    originalPrice: 299,
    image: "/src/assets/event-collage.jpg",
    rating: 4.8,
    category: "Services"
  }
];

export default function ShopSection() {
  return (
    <section id="shop" className="py-20 bg-background">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <Badge className="mb-4">Our Shop</Badge>
          <h2 className="text-4xl font-bold text-foreground mb-4">Photography Products & Services</h2>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
            Preserve your memories with our premium products and professional services
          </p>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {products.map((product) => (
            <Card key={product.id} className="group hover:shadow-primary transition-all duration-300 overflow-hidden">
              <div className="relative overflow-hidden">
                <img 
                  src={product.image} 
                  alt={product.name}
                  className="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
                />
                <div className="absolute top-3 right-3 opacity-0 group-hover:opacity-100 transition-opacity">
                  <Button size="sm" variant="secondary" className="h-8 w-8 p-0">
                    <Heart className="h-4 w-4" />
                  </Button>
                </div>
                {product.originalPrice > product.price && (
                  <div className="absolute top-3 left-3">
                    <Badge variant="destructive">
                      {Math.round(((product.originalPrice - product.price) / product.originalPrice) * 100)}% OFF
                    </Badge>
                  </div>
                )}
              </div>
              
              <CardContent className="p-6">
                <div className="mb-2">
                  <Badge variant="outline" className="text-xs">{product.category}</Badge>
                </div>
                <h3 className="text-lg font-semibold mb-2">{product.name}</h3>
                <p className="text-muted-foreground text-sm mb-3">{product.description}</p>
                
                <div className="flex items-center gap-2 mb-3">
                  <div className="flex">
                    {[...Array(5)].map((_, i) => (
                      <Star 
                        key={i} 
                        className={`h-4 w-4 ${i < Math.floor(product.rating) ? 'fill-yellow-400 text-yellow-400' : 'text-gray-300'}`} 
                      />
                    ))}
                  </div>
                  <span className="text-sm text-muted-foreground">({product.rating})</span>
                </div>

                <div className="flex items-center gap-2 mb-4">
                  <span className="text-2xl font-bold text-primary">₹{product.price}</span>
                  {product.originalPrice > product.price && (
                    <span className="text-sm text-muted-foreground line-through">₹{product.originalPrice}</span>
                  )}
                </div>

                <Button className="w-full gap-2">
                  <ShoppingCart className="h-4 w-4" />
                  Add to Cart
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>

        <div className="text-center mt-12">
          <Button variant="outline" size="lg">
            View All Products
          </Button>
        </div>
      </div>
    </section>
  );
}