import { <PERSON>, CardContent, <PERSON><PERSON>eader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Bell, Mail, MessageSquare, Calendar, CheckCircle, Clock, AlertTriangle } from "lucide-react";
import { DashboardLayout } from "@/components/layout/DashboardLayout";

const notifications = [
  {
    id: 1,
    type: "email",
    title: "New client inquiry received",
    message: "<PERSON><PERSON> submitted a wedding photography inquiry for March 2024",
    timestamp: "2 minutes ago",
    status: "unread",
    priority: "high"
  },
  {
    id: 2,
    type: "system", 
    title: "Payment received",
    message: "₹25,000 payment received for Priya & Raj wedding event",
    timestamp: "1 hour ago",
    status: "read",
    priority: "medium"
  },
  {
    id: 3,
    type: "reminder",
    title: "Event delivery due tomorrow",
    message: "<PERSON> & John wedding album delivery is due by Dec 20",
    timestamp: "3 hours ago",
    status: "unread",
    priority: "urgent"
  }
];

export default function Notifications() {
  return (
    <DashboardLayout>
      <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold text-foreground mb-2">Notifications</h1>
        <p className="text-muted-foreground">Manage all your notifications and alerts</p>
      </div>

      <div className="grid gap-4">
        {notifications.map((notification) => (
          <Card key={notification.id} className="shadow-card border-border">
            <CardContent className="p-4">
              <div className="flex items-start gap-4">
                <div className="p-2 bg-primary/10 rounded-full">
                  <Bell className="h-5 w-5 text-primary" />
                </div>
                <div className="flex-1">
                  <div className="flex items-center justify-between mb-2">
                    <h3 className="font-semibold">{notification.title}</h3>
                    <Badge variant="outline" 
                      className={notification.priority === "urgent" ? "bg-destructive/10 text-destructive" : 
                                notification.priority === "high" ? "bg-warning/10 text-warning" : 
                                "bg-success/10 text-success"}>
                      {notification.priority}
                    </Badge>
                  </div>
                  <p className="text-muted-foreground text-sm mb-2">{notification.message}</p>
                  <div className="flex items-center justify-between">
                    <span className="text-xs text-muted-foreground">{notification.timestamp}</span>
                    <Button size="sm">Mark as Read</Button>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
      </div>
    </DashboardLayout>
  );
}