import { DashboardLayout } from "@/components/layout/DashboardLayout";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { 
  Plus,
  Search,
  Filter,
  Calendar,
  MapPin,
  Users,
  Camera,
  Download,
  Share,
  MoreVertical,
  Eye,
  Edit,
  Trash2
} from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

const events = [
  {
    id: 1,
    title: "Sarah & John Wedding",
    date: "Dec 15, 2024",
    location: "Grand Ballroom, Hotel Paradise",
    type: "Wedding",
    status: "active",
    photographer: "Alex Studio",
    guests: 250,
    photos: 847,
    downloads: 245,
    revenue: "₹85,000",
    progress: 85,
    thumbnail: "/placeholder-event.jpg"
  },
  {
    id: 2,
    title: "TechCorp Annual Meet",
    date: "Dec 18, 2024",
    location: "Convention Center",
    type: "Corporate",
    status: "upcoming",
    photographer: "<PERSON> Photo",
    guests: 500,
    photos: 0,
    downloads: 0,
    revenue: "₹1,20,000",
    progress: 60,
    thumbnail: "/placeholder-event.jpg"
  },
  {
    id: 3,
    title: "Emma's Birthday Bash",
    date: "Dec 22, 2024",
    location: "Private Villa",
    type: "Birthday",
    status: "confirmed",
    photographer: "Lisa Creative",
    guests: 50,
    photos: 0,
    downloads: 0,
    revenue: "₹25,000",
    progress: 30,
    thumbnail: "/placeholder-event.jpg"
  },
  {
    id: 4,
    title: "Mumbai Fashion Week",
    date: "Nov 28, 2024",
    location: "Fashion Arena",
    type: "Fashion",
    status: "completed",
    photographer: "Pro Studios",
    guests: 300,
    photos: 1250,
    downloads: 578,
    revenue: "₹2,50,000",
    progress: 100,
    thumbnail: "/placeholder-event.jpg"
  },
  {
    id: 5,
    title: "Product Launch Event",
    date: "Dec 28, 2024",
    location: "Innovation Hub",
    type: "Corporate",
    status: "draft",
    photographer: "Alex Studio",
    guests: 150,
    photos: 0,
    downloads: 0,
    revenue: "₹75,000",
    progress: 15,
    thumbnail: "/placeholder-event.jpg"
  },
  {
    id: 6,
    title: "Anniversary Celebration",
    date: "Nov 15, 2024",
    location: "Beach Resort",
    type: "Anniversary",
    status: "completed",
    photographer: "Romantic Shots",
    guests: 80,
    photos: 425,
    downloads: 156,
    revenue: "₹45,000",
    progress: 100,
    thumbnail: "/placeholder-event.jpg"
  }
];

const statusStyles = {
  active: "bg-success/10 text-success border-success/20",
  upcoming: "bg-primary/10 text-primary border-primary/20",
  confirmed: "bg-accent/10 text-accent border-accent/20",
  completed: "bg-muted/10 text-muted-foreground border-muted/20",
  draft: "bg-warning/10 text-warning border-warning/20"
};

const typeStyles = {
  Wedding: "bg-pink-500/10 text-pink-600 border-pink-500/20",
  Corporate: "bg-blue-500/10 text-blue-600 border-blue-500/20",
  Birthday: "bg-purple-500/10 text-purple-600 border-purple-500/20",
  Fashion: "bg-orange-500/10 text-orange-600 border-orange-500/20",
  Anniversary: "bg-red-500/10 text-red-600 border-red-500/20"
};

export default function Events() {
  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row justify-between items-start gap-4">
          <div>
            <h1 className="text-3xl font-bold bg-gradient-primary bg-clip-text text-transparent">
              Events Management
            </h1>
            <p className="text-muted-foreground">
              Manage all your photography events and shoots
            </p>
          </div>
          <Button variant="gradient" className="shrink-0">
            <Plus className="h-4 w-4 mr-2" />
            Create Event
          </Button>
        </div>

        {/* Filters */}
        <Card className="shadow-card border-border">
          <CardContent className="p-4">
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search events..."
                  className="pl-10"
                />
              </div>
              <Select>
                <SelectTrigger className="w-full sm:w-[180px]">
                  <SelectValue placeholder="Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="active">Active</SelectItem>
                  <SelectItem value="upcoming">Upcoming</SelectItem>
                  <SelectItem value="completed">Completed</SelectItem>
                  <SelectItem value="draft">Draft</SelectItem>
                </SelectContent>
              </Select>
              <Select>
                <SelectTrigger className="w-full sm:w-[180px]">
                  <SelectValue placeholder="Type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Types</SelectItem>
                  <SelectItem value="wedding">Wedding</SelectItem>
                  <SelectItem value="corporate">Corporate</SelectItem>
                  <SelectItem value="birthday">Birthday</SelectItem>
                  <SelectItem value="fashion">Fashion</SelectItem>
                </SelectContent>
              </Select>
              <Button variant="outline">
                <Filter className="h-4 w-4 mr-2" />
                Filters
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Events Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {events.map((event) => (
            <Card key={event.id} className="shadow-card border-border hover:shadow-primary transition-smooth group">
              <div className="relative h-48 overflow-hidden rounded-t-lg">
                <div className="w-full h-full bg-gradient-secondary flex items-center justify-center">
                  <Camera className="h-12 w-12 text-muted-foreground" />
                </div>
                <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent" />
                <div className="absolute top-3 left-3">
                  <Badge 
                    variant="outline" 
                    className={statusStyles[event.status as keyof typeof statusStyles]}
                  >
                    {event.status}
                  </Badge>
                </div>
                <div className="absolute top-3 right-3">
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="icon" className="bg-black/20 hover:bg-black/40 text-white">
                        <MoreVertical className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem>
                        <Eye className="h-4 w-4 mr-2" />
                        View Details
                      </DropdownMenuItem>
                      <DropdownMenuItem>
                        <Edit className="h-4 w-4 mr-2" />
                        Edit Event
                      </DropdownMenuItem>
                      <DropdownMenuItem>
                        <Share className="h-4 w-4 mr-2" />
                        Share Gallery
                      </DropdownMenuItem>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem className="text-destructive">
                        <Trash2 className="h-4 w-4 mr-2" />
                        Delete
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
                <div className="absolute bottom-3 left-3 right-3">
                  <h3 className="text-white font-semibold mb-1 line-clamp-1">
                    {event.title}
                  </h3>
                  <Badge 
                    variant="outline" 
                    className={typeStyles[event.type as keyof typeof typeStyles]}
                  >
                    {event.type}
                  </Badge>
                </div>
              </div>

              <CardContent className="p-4">
                <div className="space-y-3">
                  <div className="space-y-2">
                    <div className="flex items-center gap-2 text-sm text-muted-foreground">
                      <Calendar className="h-4 w-4" />
                      {event.date}
                    </div>
                    <div className="flex items-center gap-2 text-sm text-muted-foreground">
                      <MapPin className="h-4 w-4" />
                      <span className="line-clamp-1">{event.location}</span>
                    </div>
                    <div className="flex items-center gap-2 text-sm text-muted-foreground">
                      <Camera className="h-4 w-4" />
                      {event.photographer}
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-4 py-2 border-t border-border">
                    <div className="text-center">
                      <div className="text-lg font-semibold text-foreground">{event.photos}</div>
                      <div className="text-xs text-muted-foreground">Photos</div>
                    </div>
                    <div className="text-center">
                      <div className="text-lg font-semibold text-foreground">{event.guests}</div>
                      <div className="text-xs text-muted-foreground">Guests</div>
                    </div>
                  </div>

                  <div className="flex items-center justify-between pt-2 border-t border-border">
                    <div>
                      <div className="text-sm font-medium text-foreground">{event.revenue}</div>
                      <div className="text-xs text-muted-foreground">Revenue</div>
                    </div>
                    <Button variant="outline" size="sm">
                      <Download className="h-4 w-4 mr-1" />
                      {event.downloads}
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </DashboardLayout>
  );
}