import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Textarea } from "@/components/ui/textarea";
import { 
  Globe, 
  Search, 
  Plus, 
  Eye,
  Edit,
  Upload,
  Settings,
  BarChart3,
  Users,
  MessageSquare,
  Camera,
  Image,
  FileText,
  Monitor,
  Smartphone
} from "lucide-react";
import { DashboardLayout } from "@/components/layout/DashboardLayout";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Tabs,
  Ta<PERSON>Content,
  TabsList,
  TabsTrigger,
} from "@/components/ui/tabs";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";

const websitePages = [
  { id: 1, name: "Home", url: "/", status: "published", lastModified: "2024-01-28", views: 1542 },
  { id: 2, name: "About Us", url: "/about", status: "published", lastModified: "2024-01-25", views: 342 },
  { id: 3, name: "Services", url: "/services", status: "published", lastModified: "2024-01-20", views: 567 },
  { id: 4, name: "Portfolio", url: "/portfolio", status: "published", lastModified: "2024-01-28", views: 892 },
  { id: 5, name: "Contact", url: "/contact", status: "published", lastModified: "2024-01-15", views: 234 },
  { id: 6, name: "Blog", url: "/blog", status: "draft", lastModified: "2024-01-20", views: 0 }
];

const inquiries = [
  {
    id: 1,
    name: "Rahul Sharma",
    email: "<EMAIL>",
    phone: "9876543210",
    service: "Wedding Photography",
    message: "Looking for wedding photography for March 2024",
    date: "2024-01-28",
    status: "new"
  },
  {
    id: 2,
    name: "Priya Patel",
    email: "<EMAIL>", 
    phone: "9876543211",
    service: "Baby Photoshoot",
    message: "Want to book baby photoshoot for my 6-month old",
    date: "2024-01-27",
    status: "contacted"
  },
  {
    id: 3,
    name: "TechStart Solutions",
    email: "<EMAIL>",
    phone: "9876543212", 
    service: "Corporate Event",
    message: "Need photographer for annual company event",
    date: "2024-01-26",
    status: "quoted"
  }
];

const getStatusColor = (status: string) => {
  switch (status) {
    case "published": return "bg-success/10 text-success border-success/20";
    case "draft": return "bg-warning/10 text-warning border-warning/20";
    case "new": return "bg-accent/10 text-accent border-accent/20";
    case "contacted": return "bg-warning/10 text-warning border-warning/20";
    case "quoted": return "bg-primary/10 text-primary border-primary/20";
    default: return "bg-muted/10 text-muted-foreground border-muted/20";
  }
};

export default function WebsiteAdmin() {
  const [activeTab, setActiveTab] = useState("overview");
  const [showPageEditor, setShowPageEditor] = useState(false);

  return (
    <DashboardLayout>
      <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col lg:flex-row gap-4 justify-between items-start lg:items-center">
        <div>
          <h1 className="text-3xl font-bold text-foreground mb-2">
            Website Management
          </h1>
          <p className="text-muted-foreground">
            Manage your website content, pages, and visitor inquiries
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" className="gap-2">
            <Eye className="h-4 w-4" />
            Preview Site
          </Button>
          <Button className="gap-2">
            <Plus className="h-4 w-4" />
            New Page
          </Button>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="pages">Pages</TabsTrigger>
          <TabsTrigger value="inquiries">Inquiries</TabsTrigger>
          <TabsTrigger value="media">Media</TabsTrigger>
          <TabsTrigger value="settings">Settings</TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-6">
          {/* Stats Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <Card className="shadow-card border-border">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Page Views</p>
                    <p className="text-2xl font-bold">12,543</p>
                  </div>
                  <BarChart3 className="h-8 w-8 text-primary" />
                </div>
                <p className="text-xs text-muted-foreground mt-2">+15% from last month</p>
              </CardContent>
            </Card>

            <Card className="shadow-card border-border">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Unique Visitors</p>
                    <p className="text-2xl font-bold">3,847</p>
                  </div>
                  <Users className="h-8 w-8 text-success" />
                </div>
                <p className="text-xs text-muted-foreground mt-2">+22% from last month</p>
              </CardContent>
            </Card>

            <Card className="shadow-card border-border">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Inquiries</p>
                    <p className="text-2xl font-bold">47</p>
                  </div>
                  <MessageSquare className="h-8 w-8 text-accent" />
                </div>
                <p className="text-xs text-muted-foreground mt-2">+8 new this week</p>
              </CardContent>
            </Card>

            <Card className="shadow-card border-border">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Conversion Rate</p>
                    <p className="text-2xl font-bold">4.2%</p>
                  </div>
                  <BarChart3 className="h-8 w-8 text-warning" />
                </div>
                <p className="text-xs text-muted-foreground mt-2">Inquiry to booking</p>
              </CardContent>
            </Card>
          </div>

          {/* Recent Activity */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card className="shadow-card border-border">
              <CardHeader>
                <CardTitle>Recent Page Activity</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-success/10 rounded-full">
                    <Edit className="h-4 w-4 text-success" />
                  </div>
                  <div>
                    <p className="font-medium">Portfolio page updated</p>
                    <p className="text-sm text-muted-foreground">New wedding gallery added</p>
                  </div>
                </div>
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-accent/10 rounded-full">
                    <Plus className="h-4 w-4 text-accent" />
                  </div>
                  <div>
                    <p className="font-medium">New blog post published</p>
                    <p className="text-sm text-muted-foreground">"Photography Tips for Brides"</p>
                  </div>
                </div>
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-primary/10 rounded-full">
                    <Upload className="h-4 w-4 text-primary" />
                  </div>
                  <div>
                    <p className="font-medium">Media uploaded</p>
                    <p className="text-sm text-muted-foreground">25 new portfolio images</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="shadow-card border-border">
              <CardHeader>
                <CardTitle>Top Performing Pages</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="font-medium">Home</span>
                  <span className="text-sm text-muted-foreground">1,542 views</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="font-medium">Portfolio</span>
                  <span className="text-sm text-muted-foreground">892 views</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="font-medium">Services</span>
                  <span className="text-sm text-muted-foreground">567 views</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="font-medium">About Us</span>
                  <span className="text-sm text-muted-foreground">342 views</span>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Pages Tab */}
        <TabsContent value="pages" className="space-y-6">
          <Card className="shadow-card border-border">
            <CardHeader>
              <CardTitle>Website Pages</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Page Name</TableHead>
                      <TableHead>URL</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Last Modified</TableHead>
                      <TableHead>Views</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {websitePages.map((page) => (
                      <TableRow key={page.id}>
                        <TableCell className="font-medium">{page.name}</TableCell>
                        <TableCell>{page.url}</TableCell>
                        <TableCell>
                          <Badge variant="outline" className={getStatusColor(page.status)}>
                            {page.status}
                          </Badge>
                        </TableCell>
                        <TableCell>{page.lastModified}</TableCell>
                        <TableCell>{page.views.toLocaleString()}</TableCell>
                        <TableCell>
                          <div className="flex gap-2">
                            <Button variant="outline" size="sm" onClick={() => setShowPageEditor(true)}>
                              <Edit className="h-4 w-4" />
                            </Button>
                            <Button variant="outline" size="sm">
                              <Eye className="h-4 w-4" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Inquiries Tab */}
        <TabsContent value="inquiries" className="space-y-6">
          <Card className="shadow-card border-border">
            <CardHeader>
              <CardTitle>Customer Inquiries</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Name</TableHead>
                      <TableHead>Contact</TableHead>
                      <TableHead>Service</TableHead>
                      <TableHead>Message</TableHead>
                      <TableHead>Date</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {inquiries.map((inquiry) => (
                      <TableRow key={inquiry.id}>
                        <TableCell className="font-medium">{inquiry.name}</TableCell>
                        <TableCell>
                          <div>
                            <p>{inquiry.email}</p>
                            <p className="text-sm text-muted-foreground">{inquiry.phone}</p>
                          </div>
                        </TableCell>
                        <TableCell>{inquiry.service}</TableCell>
                        <TableCell className="max-w-xs truncate">{inquiry.message}</TableCell>
                        <TableCell>{inquiry.date}</TableCell>
                        <TableCell>
                          <Badge variant="outline" className={getStatusColor(inquiry.status)}>
                            {inquiry.status}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <div className="flex gap-2">
                            <Button variant="outline" size="sm">
                              <MessageSquare className="h-4 w-4" />
                            </Button>
                            <Button size="sm">Reply</Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Media Tab */}
        <TabsContent value="media" className="space-y-6">
          <Card className="shadow-card border-border">
            <CardHeader>
              <CardTitle>Media Library</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
                {[1, 2, 3, 4, 5, 6, 7, 8].map((i) => (
                  <div key={i} className="relative aspect-square bg-muted rounded-lg overflow-hidden group">
                    <div className="w-full h-full bg-gradient-to-br from-muted to-muted-foreground/20 flex items-center justify-center">
                      <Image className="h-8 w-8 text-muted-foreground" />
                    </div>
                    <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center">
                      <Button variant="outline" size="sm">
                        <Eye className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
              <Button className="w-full mt-4 gap-2">
                <Upload className="h-4 w-4" />
                Upload New Media
              </Button>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Settings Tab */}
        <TabsContent value="settings" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card className="shadow-card border-border">
              <CardHeader>
                <CardTitle>General Settings</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="siteName">Site Name</Label>
                  <Input id="siteName" defaultValue="Om Digital Studio" />
                </div>
                <div>
                  <Label htmlFor="siteDescription">Site Description</Label>
                  <Textarea 
                    id="siteDescription" 
                    defaultValue="Professional photography studio specializing in weddings, events, and portraits"
                  />
                </div>
                <div>
                  <Label htmlFor="contactEmail">Contact Email</Label>
                  <Input id="contactEmail" defaultValue="<EMAIL>" />
                </div>
                <div>
                  <Label htmlFor="phone">Phone Number</Label>
                  <Input id="phone" defaultValue="099790 49677" />
                </div>
              </CardContent>
            </Card>

            <Card className="shadow-card border-border">
              <CardHeader>
                <CardTitle>SEO Settings</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="metaTitle">Meta Title</Label>
                  <Input id="metaTitle" defaultValue="Om Digital Studio - Professional Photography" />
                </div>
                <div>
                  <Label htmlFor="metaDescription">Meta Description</Label>
                  <Textarea 
                    id="metaDescription" 
                    defaultValue="Professional photography services in Bardoli, Gujarat. Specializing in weddings, events, portraits, and corporate photography."
                  />
                </div>
                <div>
                  <Label htmlFor="keywords">Keywords</Label>
                  <Input id="keywords" defaultValue="photography, wedding photographer, event photography, Bardoli" />
                </div>
                <div className="flex items-center justify-between">
                  <Label htmlFor="analytics">Google Analytics</Label>
                  <Switch id="analytics" />
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>

      {/* Page Editor Dialog */}
      <Dialog open={showPageEditor} onOpenChange={setShowPageEditor}>
        <DialogContent className="max-w-4xl">
          <DialogHeader>
            <DialogTitle>Edit Page Content</DialogTitle>
            <DialogDescription>
              Modify page content and layout
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="pageTitle">Page Title</Label>
              <Input id="pageTitle" defaultValue="Home" />
            </div>
            <div>
              <Label htmlFor="pageContent">Page Content</Label>
              <Textarea 
                id="pageContent" 
                rows={10}
                defaultValue="Welcome to Om Digital Studio..."
                className="min-h-[200px]"
              />
            </div>
            <div className="flex gap-2 mt-6">
              <Button variant="outline" onClick={() => setShowPageEditor(false)}>
                Cancel
              </Button>
              <Button variant="outline">Save Draft</Button>
              <Button onClick={() => setShowPageEditor(false)}>
                Publish Changes
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
      </div>
    </DashboardLayout>
  );
}